<div class="organization-management-container">
  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="loading-indicator">
    <div class="spinner"></div>
    <p>Loading organization data...</p>
  </div>

  <!-- Organization Management Content -->
  <div class="management-content" [class.loading]="isLoading">
    
    <!-- Current Organization Section -->
    <div class="current-org-section">
      <h2>Current Organization</h2>
      <div class="current-org-card" *ngIf="currentOrganization; else noOrgSelected">
        <div class="org-header">
          <div class="org-info">
            <h3>{{ organizationName }}</h3>
            <p class="subdomain">{{ organizationSubdomain }}</p>
            <span class="status" [class]="organizationStatus">{{ organizationStatus | titlecase }}</span>
          </div>
          <div class="org-branding" *ngIf="currentOrganization.branding">
            <img *ngIf="currentOrganization.branding.logoUrl" 
                 [src]="currentOrganization.branding.logoUrl" 
                 alt="Organization Logo" 
                 class="org-logo">
            <div class="color-indicator" 
                 [style.background-color]="currentOrganization.branding.primaryColor"></div>
          </div>
        </div>
        
        <div class="org-actions" *ngIf="canManage">
          <button class="btn btn-primary" (click)="refreshCurrentOrganization()" [disabled]="isLoading">
            Refresh
          </button>
          <button class="btn btn-info" (click)="loadOrganizationDetails(currentOrganization._id)" [disabled]="isLoading">
            Load Details
          </button>
          <button class="btn btn-danger" (click)="deleteOrganization()" [disabled]="isLoading">
            Delete
          </button>
        </div>
      </div>
      
      <ng-template #noOrgSelected>
        <div class="no-org-selected">
          <p>No organization selected. Please select an organization from your list below.</p>
        </div>
      </ng-template>
    </div>

    <!-- User Organizations Section -->
    <div class="user-orgs-section">
      <h2>Your Organizations</h2>
      <div class="organizations-grid" *ngIf="userOrganizations.length > 0; else noUserOrgs">
        <div class="org-card" 
             *ngFor="let org of userOrganizations" 
             [class.selected]="currentOrganization?._id === org._id"
             (click)="selectOrganization(org)">
          <h4>{{ org.name }}</h4>
          <p>{{ org.subdomain }}</p>
          <span class="status" [class]="org.status">{{ org.status | titlecase }}</span>
          <div class="branding" *ngIf="org.branding">
            <div class="color-indicator" [style.background-color]="org.branding.primaryColor"></div>
          </div>
        </div>
      </div>
      
      <ng-template #noUserOrgs>
        <div class="no-organizations">
          <p>You are not a member of any organizations yet.</p>
        </div>
      </ng-template>
    </div>

    <!-- Create Organization Section -->
    <div class="create-org-section">
      <h2>Create New Organization</h2>
      <form class="create-org-form" (ngSubmit)="createOrganization()">
        <div class="form-group">
          <label for="orgName">Organization Name *</label>
          <input type="text" 
                 id="orgName" 
                 [(ngModel)]="createOrgForm.name" 
                 name="orgName"
                 placeholder="Enter organization name"
                 required>
        </div>
        
        <div class="form-group">
          <label for="orgSubdomain">Subdomain *</label>
          <input type="text" 
                 id="orgSubdomain" 
                 [(ngModel)]="createOrgForm.subdomain" 
                 name="orgSubdomain"
                 placeholder="Enter unique subdomain"
                 pattern="[a-z0-9-]+"
                 required>
          <small>Only lowercase letters, numbers, and hyphens allowed</small>
        </div>
        
        <div class="form-group">
          <label for="orgLogo">Logo URL</label>
          <input type="url" 
                 id="orgLogo" 
                 [(ngModel)]="createOrgForm.branding!.logoUrl" 
                 name="orgLogo"
                 placeholder="https://example.com/logo.png">
        </div>
        
        <div class="form-group">
          <label for="orgColor">Primary Color</label>
          <input type="color" 
                 id="orgColor" 
                 [(ngModel)]="createOrgForm.branding!.primaryColor" 
                 name="orgColor">
        </div>
        
        <button type="submit" class="btn btn-success" [disabled]="isLoading">
          {{ isLoading ? 'Creating...' : 'Create Organization' }}
        </button>
        <button type="button" class="btn btn-secondary" (click)="resetCreateForm()">
          Reset
        </button>
      </form>
    </div>

    <!-- Update Organization Section -->
    <div class="update-org-section" *ngIf="currentOrganization && canManage">
      <h2>Update Organization</h2>
      <form class="update-org-form" (ngSubmit)="updateOrganization()">
        <div class="form-group">
          <label for="updateOrgName">Organization Name</label>
          <input type="text" 
                 id="updateOrgName" 
                 [(ngModel)]="updateOrgForm.name" 
                 name="updateOrgName"
                 placeholder="Enter organization name">
        </div>
        
        <div class="form-group">
          <label for="updateOrgLogo">Logo URL</label>
          <input type="url" 
                 id="updateOrgLogo" 
                 [(ngModel)]="updateOrgForm.branding!.logoUrl" 
                 name="updateOrgLogo"
                 placeholder="https://example.com/logo.png">
        </div>
        
        <div class="form-group">
          <label for="updateOrgColor">Primary Color</label>
          <input type="color" 
                 id="updateOrgColor" 
                 [(ngModel)]="updateOrgForm.branding!.primaryColor" 
                 name="updateOrgColor">
        </div>
        
        <button type="submit" class="btn btn-primary" [disabled]="isLoading">
          {{ isLoading ? 'Updating...' : 'Update Organization' }}
        </button>
      </form>
    </div>

    <!-- Member Management Section -->
    <div class="member-management-section" *ngIf="currentOrganization && canManage">
      <h2>Member Management</h2>
      
      <!-- Add User Form -->
      <div class="add-user-form">
        <h3>Add User to Organization</h3>
        <form (ngSubmit)="addUserToOrganization()">
          <div class="form-row">
            <div class="form-group">
              <label for="userEmail">User Email</label>
              <input type="email" 
                     id="userEmail" 
                     [(ngModel)]="addUserForm.email" 
                     name="userEmail"
                     placeholder="<EMAIL>"
                     required>
            </div>
            
            <div class="form-group">
              <label for="userRole">Role</label>
              <select id="userRole" [(ngModel)]="addUserForm.role" name="userRole">
                <option value="orgmember">Organization Member</option>
                <option value="admin">Admin</option>
                <option value="super_user" *ngIf="isGodSuperUser">Super User</option>
              </select>
            </div>
            
            <button type="submit" class="btn btn-success" [disabled]="isLoading">
              Add User
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- All Organizations Section (God Super User only) -->
    <div class="all-orgs-section" *ngIf="isGodSuperUser">
      <h2>All Organizations (Admin View)</h2>
      <div class="organizations-grid" *ngIf="allOrganizations.length > 0">
        <div class="org-card admin-view" *ngFor="let org of allOrganizations">
          <h4>{{ org.name }}</h4>
          <p>{{ org.subdomain }}</p>
          <span class="status" [class]="org.status">{{ org.status | titlecase }}</span>
          <div class="admin-actions">
            <button class="btn btn-sm btn-info" (click)="selectOrganization(org)">
              Select
            </button>
            <button class="btn btn-sm btn-primary" (click)="loadOrganizationDetails(org._id)">
              Details
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Permissions Info -->
    <div class="permissions-info">
      <h3>Your Permissions</h3>
      <div class="permission-badges">
        <span class="badge" [class.active]="isAdmin">Organization Admin</span>
        <span class="badge" [class.active]="isGodSuperUser">God Super User</span>
        <span class="badge" [class.active]="canManage">Can Manage Organizations</span>
      </div>
    </div>
  </div>
</div>
