import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';

import { UserDataService } from './user-data.service';
import { AuthService } from '../auth/auth.service';
import { UserService } from '../user/user.service';
import {
  User,
  CompleteUser,
  UserProfileUpdateRequest,
  UserProfileUpdateResponse,
  DeleteUserResponse,
} from '../../models/user.model';

describe('UserDataService', () => {
  let service: UserDataService;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let userServiceSpy: jasmine.SpyObj<UserService>;

  const mockUser: User = {
    _id: '123',
    JWT_UID: 'jwt123',
    email: '<EMAIL>',
    name: 'Test User',
    phone_number: '+1234567890',
    roles: [],
  };

  const mockCompleteUser: CompleteUser = {
    _id: '123',
    name: 'Test User',
    email: '<EMAIL>',
    phone_number: '+1234567890',
    type: 'individual',
    status: 'active',
    profile: {
      fullName: 'Test User Full',
      jobTitle: 'Developer',
      companyName: 'Test Corp',
      bio: 'Test bio',
      industryTags: ['Technology'],
      networkingGoal: 'Test goal',
    },
    roles: [],
    systemPrivileges: [],
    organizations: [],
    privilegeSummary: {
      isGodSuperUser: false,
      hasSystemPrivileges: false,
      highestPrivilegeLevel: 0,
      organizationCount: 0,
    },
  };

  beforeEach(() => {
    const authSpy = jasmine.createSpyObj(
      'AuthService',
      [
        'getCurrentUser',
        'getCompleteUser',
        'isAuthenticated',
        'refreshCompleteUserData',
        'loadCompleteUserData',
        'logout',
      ],
      {
        currentUser$: of(mockUser),
        completeUser$: of(mockCompleteUser),
        isLoading$: of(false),
      }
    );

    const userSpy = jasmine.createSpyObj('UserService', [
      'updateCurrentUser',
      'deleteCurrentUser',
    ]);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        UserDataService,
        { provide: AuthService, useValue: authSpy },
        { provide: UserService, useValue: userSpy },
      ],
    });

    service = TestBed.inject(UserDataService);
    authServiceSpy = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    userServiceSpy = TestBed.inject(UserService) as jasmine.SpyObj<UserService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getCurrentUser', () => {
    it('should return current user from auth service', () => {
      authServiceSpy.getCurrentUser.and.returnValue(mockUser);

      const result = service.getCurrentUser();

      expect(result).toEqual(mockUser);
      expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
    });
  });

  describe('getCompleteUser', () => {
    it('should return complete user from auth service', () => {
      authServiceSpy.getCompleteUser.and.returnValue(mockCompleteUser);

      const result = service.getCompleteUser();

      expect(result).toEqual(mockCompleteUser);
      expect(authServiceSpy.getCompleteUser).toHaveBeenCalled();
    });
  });

  describe('isAuthenticated', () => {
    it('should return authentication status from auth service', () => {
      authServiceSpy.isAuthenticated.and.returnValue(true);

      const result = service.isAuthenticated();

      expect(result).toBe(true);
      expect(authServiceSpy.isAuthenticated).toHaveBeenCalled();
    });
  });

  describe('refreshUserData', () => {
    it('should refresh user data and update loading state', () => {
      authServiceSpy.refreshCompleteUserData.and.returnValue(
        of(mockCompleteUser)
      );

      service.refreshUserData().subscribe((result) => {
        expect(result).toEqual(mockCompleteUser);
      });

      expect(authServiceSpy.refreshCompleteUserData).toHaveBeenCalled();
    });

    it('should handle errors and update loading state', () => {
      const error = new Error('Refresh failed');
      authServiceSpy.refreshCompleteUserData.and.returnValue(
        throwError(() => error)
      );

      service.refreshUserData().subscribe({
        error: (err) => {
          expect(err).toEqual(error);
        },
      });
    });
  });

  describe('updateProfile', () => {
    it('should update profile and refresh user data', () => {
      const updateRequest: UserProfileUpdateRequest = {
        name: 'Updated Name',
        email: '<EMAIL>',
      };

      const updateResponse: UserProfileUpdateResponse = {
        message: 'Profile updated',
        user: mockCompleteUser,
      };

      userServiceSpy.updateCurrentUser.and.returnValue(of(updateResponse));
      authServiceSpy.refreshCompleteUserData.and.returnValue(
        of(mockCompleteUser)
      );

      service.updateProfile(updateRequest).subscribe((result) => {
        expect(result).toEqual(updateResponse);
      });

      expect(userServiceSpy.updateCurrentUser).toHaveBeenCalledWith(
        updateRequest
      );
    });
  });

  describe('deleteAccount', () => {
    it('should delete account and logout user', () => {
      const deleteResponse: DeleteUserResponse = {
        message: 'Account deleted',
      };

      userServiceSpy.deleteCurrentUser.and.returnValue(of(deleteResponse));

      service.deleteAccount().subscribe((result) => {
        expect(result).toEqual(deleteResponse);
      });

      expect(userServiceSpy.deleteCurrentUser).toHaveBeenCalled();
      expect(authServiceSpy.logout).toHaveBeenCalled();
    });
  });

  describe('userApi getter', () => {
    it('should return UserService instance for direct API access', () => {
      const userApi = service.userApi;

      expect(userApi).toBe(userServiceSpy);
    });
  });

  describe('getUserPermissions', () => {
    it('should return user permissions based on complete user data', () => {
      service.getUserPermissions().subscribe((permissions) => {
        expect(permissions).toEqual({
          isAdmin: false,
          isGodSuperUser: false,
          hasSystemPrivileges: false,
          organizationCount: 0,
          roles: [],
        });
      });
    });

    it('should return null when no complete user data', () => {
      Object.defineProperty(service, 'completeUser$', {
        value: of(null),
      });

      service.getUserPermissions().subscribe((permissions) => {
        expect(permissions).toBeNull();
      });
    });
  });

  describe('hasRole', () => {
    it('should check if user has specific role', () => {
      service.hasRole('ADMIN').subscribe((hasRole) => {
        expect(hasRole).toBe(false);
      });
    });
  });

  describe('isAdmin', () => {
    it('should check if user is admin', () => {
      service.isAdmin().subscribe((isAdmin) => {
        expect(isAdmin).toBe(false);
      });
    });
  });

  describe('initializeUserData', () => {
    it('should return null when not authenticated', () => {
      authServiceSpy.isAuthenticated.and.returnValue(false);

      service.initializeUserData().subscribe((result) => {
        expect(result).toBeNull();
      });
    });

    it('should return existing complete user when available', () => {
      authServiceSpy.isAuthenticated.and.returnValue(true);
      authServiceSpy.getCompleteUser.and.returnValue(mockCompleteUser);

      service.initializeUserData().subscribe((result) => {
        expect(result).toEqual(mockCompleteUser);
      });
    });

    it('should load complete user data when not in storage', () => {
      authServiceSpy.isAuthenticated.and.returnValue(true);
      authServiceSpy.getCompleteUser.and.returnValue(null);
      authServiceSpy.loadCompleteUserData.and.returnValue(of(mockCompleteUser));

      service.initializeUserData().subscribe((result) => {
        expect(result).toEqual(mockCompleteUser);
      });

      expect(authServiceSpy.loadCompleteUserData).toHaveBeenCalled();
    });
  });
});
