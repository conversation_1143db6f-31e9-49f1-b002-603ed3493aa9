import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { UserDataService } from '../../../../core/services/user-data/user-data.service';
import { AuthService } from '../../../../core/services/auth/auth.service';

@Component({
  selector: 'app-account-type-selection',
  templateUrl: './account-type-selection.component.html',
  styleUrls: ['./account-type-selection.component.css'],
})
export class AccountTypeSelectionComponent implements OnInit {
  accountTypeForm!: FormGroup;
  loading = false;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private userDataService: UserDataService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.accountTypeForm = this.formBuilder.group({
      accountType: ['', Validators.required],
    });

    this.loadExistingUserType();
  }

  private loadExistingUserType() {
    // Load complete user data to check existing type
    this.userDataService.loadCompleteUserData().subscribe({
      next: (completeUser) => {
        if (completeUser && completeUser.type) {
          // Pre-select the existing account type
          this.accountTypeForm.patchValue({
            accountType: completeUser.type,
          });

          // If user already has a type and some profile data,
          // redirect them to the appropriate profile setup
          if (
            completeUser.type === 'individual' &&
            !this.authService.isOnboardingCompleted()
          ) {
            // User has individual type but incomplete profile
            this.router.navigate(['/onboarding/individual-profile']);
          } else if (
            completeUser.type === 'organization' &&
            !this.authService.isOnboardingCompleted()
          ) {
            // User has organization type but incomplete profile
            this.router.navigate(['/onboarding/organization-profile']);
          }
        }
      },
      error: (error) => {
        console.error('Error loading user type:', error);
        // Continue with empty form if there's an error
      },
    });
  }

  onContinue() {
    if (this.accountTypeForm.invalid) {
      return;
    }

    const accountType = this.accountTypeForm.get('accountType')?.value;
    this.loading = true;

    // Save the account type to the backend
    this.userDataService.updateProfile({ type: accountType }).subscribe({
      next: () => {
        this.loading = false;
        // Navigate to the appropriate profile setup
        if (accountType === 'individual') {
          this.router.navigate(['/onboarding/individual-profile']);
        } else if (accountType === 'organization') {
          this.router.navigate(['/onboarding/organization-profile']);
        }
      },
      error: (error) => {
        this.loading = false;
        console.error('Error saving account type:', error);
        // Still navigate even if save fails, user can retry later
        if (accountType === 'individual') {
          this.router.navigate(['/onboarding/individual-profile']);
        } else if (accountType === 'organization') {
          this.router.navigate(['/onboarding/organization-profile']);
        }
      },
    });
  }

  selectAccountType(type: string) {
    this.accountTypeForm.patchValue({ accountType: type });
  }

  isSelected(type: string): boolean {
    return this.accountTypeForm.get('accountType')?.value === type;
  }
}
