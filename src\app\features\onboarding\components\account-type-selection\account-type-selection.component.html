<div class="max-w-2xl mx-auto">
  <div class="text-center mb-8">
    <h2 class="text-2xl font-bold text-gray-900">Choose Your Account Type</h2>
    <p class="mt-2 text-gray-600">Select the type of account that best describes you</p>
  </div>

  <form [formGroup]="accountTypeForm" (ngSubmit)="onContinue()">
    <div class="space-y-6">
      <!-- Individual Account Option -->
      <div 
        (click)="selectAccountType('individual')"
        [class]="'border-2 rounded-lg p-6 cursor-pointer transition-all duration-200 ' + 
                 (isSelected('individual') ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-gray-300')"
      >
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <input
              id="individual"
              name="accountType"
              type="radio"
              value="individual"
              formControlName="accountType"
              class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
            />
          </div>
          <div class="ml-3">
            <label for="individual" class="text-lg font-medium text-gray-900 cursor-pointer">
              Individual Account
            </label>
            <p class="text-gray-600 mt-1">
              Perfect for professionals looking to network, attend events, and build connections.
            </p>
            <ul class="mt-3 text-sm text-gray-500 space-y-1">
              <li>• Personal profile and networking</li>
              <li>• Event attendance and participation</li>
              <li>• Connection management</li>
              <li>• Delegate email functionality</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Organization Account Option -->
      <div 
        (click)="selectAccountType('organization')"
        [class]="'border-2 rounded-lg p-6 cursor-pointer transition-all duration-200 ' + 
                 (isSelected('organization') ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-gray-300')"
      >
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <input
              id="organization"
              name="accountType"
              type="radio"
              value="organization"
              formControlName="accountType"
              class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
            />
          </div>
          <div class="ml-3">
            <label for="organization" class="text-lg font-medium text-gray-900 cursor-pointer">
              Organization Account
            </label>
            <p class="text-gray-600 mt-1">
              Ideal for companies that want to sponsor events, organize gatherings, or manage teams.
            </p>
            <ul class="mt-3 text-sm text-gray-500 space-y-1">
              <li>• Custom subdomain (yourcompany.digimeet.io)</li>
              <li>• Event creation and management</li>
              <li>• Team member management</li>
              <li>• Sponsorship and lead capture</li>
              <li>• Branded organization profile</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Continue Button -->
    <div class="mt-8 flex justify-center">
      <button
        type="submit"
        [disabled]="accountTypeForm.invalid || loading"
        class="px-8 py-3 bg-indigo-600 text-white font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Continue
      </button>
    </div>
  </form>
</div>
