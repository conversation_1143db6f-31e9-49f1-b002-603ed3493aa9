import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { OrganisationService } from '../organisation/organisation.service';
import { UserDataService } from '../user-data/user-data.service';
import {
  Organization,
  CreateOrganizationRequest,
  UpdateOrganizationRequest,
  OrganizationDetailsResponse,
  CreateOrganizationResponse,
  OrganizationOperationResponse,
} from '../../models/organization.model';

@Injectable({
  providedIn: 'root',
})
export class OrganizationDataService {
  private isLoadingSubject = new BehaviorSubject<boolean>(false);
  public isLoading$ = this.isLoadingSubject.asObservable();

  private currentOrganizationSubject = new BehaviorSubject<Organization | null>(
    null
  );
  public currentOrganization$ = this.currentOrganizationSubject.asObservable();

  constructor(
    private organizationService: OrganisationService,
    private userDataService: UserDataService
  ) {}

  /**
   * Get current organization
   */
  getCurrentOrganization(): Organization | null {
    return this.currentOrganizationSubject.value;
  }

  /**
   * Set current organization
   */
  setCurrentOrganization(organization: Organization | null): void {
    this.currentOrganizationSubject.next(organization);
  }

  /**
   * Check if user is organization admin
   */
  isOrganizationAdmin(): Observable<boolean> {
    return this.userDataService.getUserPermissions().pipe(
      map((permissions) => {
        if (!permissions) return false;
        return permissions.roles.some(
          (role) => role.includes('admin') || role.includes('super_user')
        );
      })
    );
  }

  /**
   * Check if user is God Super User
   */
  isGodSuperUser(): Observable<boolean> {
    return this.userDataService
      .getUserPermissions()
      .pipe(map((permissions) => permissions?.isGodSuperUser || false));
  }

  /**
   * Create organization with loading state management
   */
  createOrganization(
    organizationData: CreateOrganizationRequest
  ): Observable<CreateOrganizationResponse> {
    this.isLoadingSubject.next(true);
    return this.organizationService.createOrganization(organizationData).pipe(
      tap((response) => {
        if (response.success) {
          this.setCurrentOrganization(response.organization);
        }
        this.isLoadingSubject.next(false);
      }),
      catchError((error) => {
        this.isLoadingSubject.next(false);
        throw error;
      })
    );
  }

  /**
   * Create organization by user with loading state management
   */
  createOrganizationByUser(
    organizationData: CreateOrganizationRequest
  ): Observable<CreateOrganizationResponse> {
    console.log(
      'OrganizationDataService.createOrganizationByUser called with:',
      organizationData
    );
    this.isLoadingSubject.next(true);
    return this.organizationService
      .createOrganizationByUser(organizationData)
      .pipe(
        tap((response) => {
          console.log(
            'OrganizationDataService.createOrganizationByUser response:',
            response
          );
          if (response.success) {
            this.setCurrentOrganization(response.organization);
          }
          this.isLoadingSubject.next(false);
        }),
        catchError((error) => {
          console.error(
            'OrganizationDataService.createOrganizationByUser error:',
            error
          );
          this.isLoadingSubject.next(false);
          throw error;
        })
      );
  }

  /**
   * Update organization with automatic refresh
   */
  updateOrganization(
    idOrSub: string,
    updateData: UpdateOrganizationRequest
  ): Observable<Organization> {
    this.isLoadingSubject.next(true);
    return this.organizationService
      .updateOrganization(idOrSub, updateData)
      .pipe(
        tap((organization) => {
          this.setCurrentOrganization(organization);
          this.isLoadingSubject.next(false);
        }),
        catchError((error) => {
          this.isLoadingSubject.next(false);
          throw error;
        })
      );
  }

  /**
   * Delete organization with cleanup
   */
  deleteOrganization(
    idOrSub: string
  ): Observable<OrganizationOperationResponse> {
    this.isLoadingSubject.next(true);
    return this.organizationService.deleteOrganization(idOrSub).pipe(
      tap((response) => {
        if (response.success) {
          this.setCurrentOrganization(null);
        }
        this.isLoadingSubject.next(false);
      }),
      catchError((error) => {
        this.isLoadingSubject.next(false);
        throw error;
      })
    );
  }

  /**
   * Load organization details and set as current
   */
  loadOrganizationDetails(
    orgIdOrSubdomain: string
  ): Observable<OrganizationDetailsResponse> {
    this.isLoadingSubject.next(true);
    return this.organizationService
      .getOrganizationDetails(orgIdOrSubdomain)
      .pipe(
        tap((response) => {
          if (response.success) {
            this.setCurrentOrganization(response.data);
          }
          this.isLoadingSubject.next(false);
        }),
        catchError((error) => {
          this.isLoadingSubject.next(false);
          throw error;
        })
      );
  }

  /**
   * Get user's organizations from user data
   */
  getUserOrganizations(): Observable<Organization[]> {
    return this.userDataService.getUserOrganizations();
  }

  /**
   * Check if user can manage organization
   */
  canManageOrganization(): Observable<boolean> {
    return this.userDataService.getUserPermissions().pipe(
      map((permissions) => {
        if (!permissions) return false;

        // God Super User can manage any organization
        if (permissions.isGodSuperUser) return true;

        // Check if user has admin role in the specific organization
        return permissions.roles.some(
          (role) => role.includes('admin') || role.includes('super_user')
        );
      })
    );
  }

  /**
   * Get OrganisationService instance for direct API access
   */
  get organizationApi(): OrganisationService {
    return this.organizationService;
  }

  /**
   * Refresh current organization data
   */
  refreshCurrentOrganization(): Observable<Organization | null> {
    const currentOrg = this.getCurrentOrganization();
    if (!currentOrg) {
      return of(null);
    }

    this.isLoadingSubject.next(true);
    return this.organizationService.getOrganizationById(currentOrg._id).pipe(
      tap((organization) => {
        this.setCurrentOrganization(organization);
        this.isLoadingSubject.next(false);
      }),
      catchError((error) => {
        this.isLoadingSubject.next(false);
        throw error;
      })
    );
  }

  /**
   * Clear current organization
   */
  clearCurrentOrganization(): void {
    this.setCurrentOrganization(null);
  }

  /**
   * Initialize organization data based on user's organizations
   */
  initializeOrganizationData(): Observable<Organization | null> {
    return this.getUserOrganizations().pipe(
      map((organizations) => {
        if (organizations && organizations.length > 0) {
          // Set the first organization as current by default
          const firstOrg = organizations[0];
          this.setCurrentOrganization(firstOrg);
          return firstOrg;
        }
        return null;
      }),
      catchError((error) => {
        console.warn('Failed to initialize organization data:', error);
        return of(null);
      })
    );
  }
}
