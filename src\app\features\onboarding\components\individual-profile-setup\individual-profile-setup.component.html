<div class="max-w-3xl mx-auto">
  <div class="text-center mb-8">
    <h2 class="text-2xl font-bold text-gray-900">
      Set Up Your Individual Profile
    </h2>
    <p class="mt-2 text-gray-600">
      Tell us about yourself to help others connect with you
    </p>
  </div>

  <!-- Alert for errors -->
  <app-alert
    *ngIf="showAlert"
    [type]="alertType"
    [message]="alertMessage"
    [dismissible]="true"
  ></app-alert>

  <form [formGroup]="profileForm" (ngSubmit)="onSubmit()" class="space-y-6">
    <!-- Full Name -->
    <div>
      <label for="fullName" class="block text-sm font-medium text-gray-700">
        Full Name *
      </label>
      <input
        id="fullName"
        type="text"
        formControlName="fullName"
        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        placeholder="Enter your full name"
      />
      <div
        *ngIf="
          profileForm.get('fullName')?.invalid &&
          profileForm.get('fullName')?.touched
        "
        class="mt-1 text-sm text-red-600"
      >
        Full name is required
      </div>
    </div>

    <!-- Job Title -->
    <div>
      <label for="jobTitle" class="block text-sm font-medium text-gray-700">
        Job Title *
      </label>
      <input
        id="jobTitle"
        type="text"
        formControlName="jobTitle"
        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        placeholder="e.g., Software Engineer, Marketing Manager"
      />
      <div
        *ngIf="
          profileForm.get('jobTitle')?.invalid &&
          profileForm.get('jobTitle')?.touched
        "
        class="mt-1 text-sm text-red-600"
      >
        Job title is required
      </div>
    </div>

    <!-- Company Name -->
    <div>
      <label for="companyName" class="block text-sm font-medium text-gray-700">
        Company Name *
      </label>
      <input
        id="companyName"
        type="text"
        formControlName="companyName"
        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        placeholder="Enter your company name"
      />
      <div
        *ngIf="
          profileForm.get('companyName')?.invalid &&
          profileForm.get('companyName')?.touched
        "
        class="mt-1 text-sm text-red-600"
      >
        Company name is required
      </div>
    </div>

    <!-- Bio -->
    <div>
      <label for="bio" class="block text-sm font-medium text-gray-700">
        One-sentence Bio *
      </label>
      <textarea
        id="bio"
        formControlName="bio"
        rows="3"
        maxlength="200"
        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        placeholder="Describe yourself in one sentence..."
      ></textarea>
      <div class="mt-1 flex justify-between">
        <div
          *ngIf="
            profileForm.get('bio')?.invalid && profileForm.get('bio')?.touched
          "
          class="text-sm text-red-600"
        >
          Bio is required
        </div>
        <div class="text-sm text-gray-500">
          {{ bioCharacterCount }}/200 characters
        </div>
      </div>
    </div>

    <!-- Industry/Function Tags -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-2">
        Industry/Function Tags * (Select up to 5)
      </label>

      <!-- Selected Tags -->
      <div class="mb-3">
        <div class="flex flex-wrap gap-2">
          <span
            *ngFor="let tag of selectedTags"
            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800"
          >
            {{ tag }}
            <button
              type="button"
              (click)="removeTag(tag)"
              class="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-indigo-400 hover:text-indigo-600 hover:bg-indigo-200"
            >
              ×
            </button>
          </span>
        </div>
        <div
          *ngIf="selectedTags.length === 0"
          class="text-sm text-red-600 mt-1"
        >
          Please select at least one industry tag
        </div>
      </div>

      <!-- Available Tags -->
      <div class="mb-3">
        <div class="flex flex-wrap gap-2">
          <button
            *ngFor="let tag of availableTags"
            type="button"
            (click)="addTag(tag)"
            [disabled]="selectedTags.includes(tag) || selectedTags.length >= 5"
            class="px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ tag }}
          </button>
        </div>
      </div>

      <!-- Custom Tag Input -->
      <div>
        <input
          type="text"
          (keydown)="addCustomTag($event)"
          [disabled]="selectedTags.length >= 5"
          class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:opacity-50"
          placeholder="Type custom tag and press Enter"
        />
        <p class="mt-1 text-xs text-gray-500">
          Type a custom tag and press Enter to add it
        </p>
      </div>
    </div>

    <!-- Networking Goal -->
    <div>
      <label
        for="networkingGoal"
        class="block text-sm font-medium text-gray-700"
      >
        Networking Goal *
      </label>
      <select
        id="networkingGoal"
        formControlName="networkingGoal"
        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
      >
        <option value="">Select your primary networking goal</option>
        <option *ngFor="let goal of networkingGoals" [value]="goal">
          {{ goal }}
        </option>
      </select>
      <div
        *ngIf="
          profileForm.get('networkingGoal')?.invalid &&
          profileForm.get('networkingGoal')?.touched
        "
        class="mt-1 text-sm text-red-600"
      >
        Please select a networking goal
      </div>
    </div>

    <!-- Delegate Email -->
    <div>
      <label
        for="delegateEmail"
        class="block text-sm font-medium text-gray-700"
      >
        Delegate Email (Optional)
      </label>
      <input
        id="delegateEmail"
        type="email"
        formControlName="delegateEmail"
        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        placeholder="<EMAIL>"
      />
      <p class="mt-1 text-xs text-gray-500">
        Email address of someone who can manage your calendar and connections
      </p>
      <div
        *ngIf="
          profileForm.get('delegateEmail')?.invalid &&
          profileForm.get('delegateEmail')?.touched
        "
        class="mt-1 text-sm text-red-600"
      >
        Please enter a valid email address
      </div>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-center pt-6">
      <button
        type="submit"
        [disabled]="loading || profileForm.invalid || selectedTags.length === 0"
        class="px-8 py-3 bg-indigo-600 text-white font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span
          *ngIf="loading"
          class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
        ></span>
        Complete Profile Setup
      </button>
    </div>
  </form>
</div>
