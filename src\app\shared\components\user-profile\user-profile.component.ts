import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { UserDataService } from '../../../core/services/user-data/user-data.service';
import {
  CompleteUser,
  UserProfileUpdateRequest,
} from '../../../core/models/user.model';

@Component({
  selector: 'app-user-profile',
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.css'],
})
export class UserProfileComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  completeUser: CompleteUser | null = null;
  isLoading = false;
  isAdmin = false;
  userPermissions: any = null;

  constructor(private userDataService: UserDataService) {}

  ngOnInit(): void {
    // Subscribe to complete user data
    this.userDataService.completeUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe((user) => {
        this.completeUser = user;
      });

    // Subscribe to loading state
    this.userDataService.isLoading$
      .pipe(takeUntil(this.destroy$))
      .subscribe((loading) => {
        this.isLoading = loading;
      });

    // Check user permissions
    this.userDataService
      .getUserPermissions()
      .pipe(takeUntil(this.destroy$))
      .subscribe((permissions) => {
        this.userPermissions = permissions;
        this.isAdmin = permissions?.isAdmin || false;
      });

    // Load user data if not already loaded
    if (!this.completeUser) {
      this.refreshUserData();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  refreshUserData(): void {
    this.userDataService.refreshUserData().subscribe({
      next: (user) => {
        console.log('User data refreshed:', user);
      },
      error: (error) => {
        console.error('Failed to refresh user data:', error);
      },
    });
  }

  updateProfile(): void {
    if (!this.completeUser) return;

    const updateData: UserProfileUpdateRequest = {
      name: this.completeUser.name,
      email: this.completeUser.email,
      phone_number: this.completeUser.phone_number,
      bio: this.completeUser.profile?.bio,
      jobTitle: this.completeUser.profile?.jobTitle,
      companyName: this.completeUser.profile?.companyName,
      // Add other fields as needed
    };

    this.userDataService.updateProfile(updateData).subscribe({
      next: (response) => {
        console.log('Profile updated successfully:', response);
      },
      error: (error) => {
        console.error('Failed to update profile:', error);
      },
    });
  }

  deleteAccount(): void {
    if (
      confirm(
        'Are you sure you want to delete your account? This action cannot be undone.'
      )
    ) {
      this.userDataService.deleteAccount().subscribe({
        next: (response) => {
          console.log('Account deleted successfully:', response);
          // User will be automatically logged out
        },
        error: (error) => {
          console.error('Failed to delete account:', error);
        },
      });
    }
  }

  // Helper methods for template
  get userName(): string {
    return this.completeUser?.name || 'Unknown User';
  }

  get userEmail(): string {
    return this.completeUser?.email || '';
  }

  get userBio(): string {
    return this.completeUser?.profile?.bio || 'No bio available';
  }

  get userOrganizations(): any[] {
    return this.completeUser?.organizations || [];
  }

  get userRoles(): string[] {
    return this.completeUser?.roles?.map((r) => r.role) || [];
  }

  hasRole(role: string): boolean {
    return this.userRoles.includes(role);
  }
}
