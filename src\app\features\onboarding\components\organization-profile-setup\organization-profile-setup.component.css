/* Organization profile setup specific styles */
.step-indicator {
  transition: all 0.3s ease;
}

.step-indicator.active {
  background-color: #4f46e5;
  color: white;
}

.step-indicator.completed {
  background-color: #10b981;
  color: white;
}

.progress-line {
  transition: all 0.3s ease;
}

.progress-line.active {
  background-color: #4f46e5;
}

.file-upload-area {
  transition: all 0.2s ease;
}

.file-upload-area:hover {
  border-color: #6b7280;
}

.subdomain-preview {
  font-weight: 600;
  color: #4f46e5;
}

.team-member-card {
  transition: all 0.2s ease;
}

.team-member-card:hover {
  background-color: #f9fafb;
}

.role-checkbox {
  accent-color: #4f46e5;
}

.form-section {
  margin-bottom: 2rem;
}

.step-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
