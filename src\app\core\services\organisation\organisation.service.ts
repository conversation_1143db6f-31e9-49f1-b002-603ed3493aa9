import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { ApiService } from '../api/api.service';
import {
  Organization,
  CreateOrganizationRequest,
  UpdateOrganizationRequest,
  PaginatedOrganizationsResponse,
  PendingOrganizationsResponse,
  OrganizationDetailsResponse,
  AddUserToOrganizationRequest,
  RemoveUserFromOrganizationRequest,
  MemberManagementRequest,
  CreateOrganizationResponse,
  ApproveOrganizationResponse,
  MemberManagementResponse,
  OrganizationOperationResponse,
} from '../../models/organization.model';

@Injectable({
  providedIn: 'root',
})
export class OrganisationService {
  private readonly path = '/organizations';

  constructor(private apiService: ApiService) {}

  /**
   * Create a new organization (subdomain is unique)
   * POST /api/v1/organizations
   */
  createOrganization(
    organizationData: CreateOrganizationRequest
  ): Observable<CreateOrganizationResponse> {
    return this.apiService.post<CreateOrganizationResponse>(
      this.path,
      organizationData
    );
  }

  /**
   * Get all organizations (paginated)
   * GET /api/v1/organizations
   */
  getAllOrganizations(
    page: number = 1,
    limit: number = 10
  ): Observable<PaginatedOrganizationsResponse> {
    const params = { page, limit };
    return this.apiService.get<PaginatedOrganizationsResponse>(
      this.path,
      params
    );
  }

  /**
   * Get all pending organizations awaiting approval (God Super User only)
   * GET /api/v1/organizations/pending
   */
  getPendingOrganizations(
    page: number = 1,
    limit: number = 10
  ): Observable<PendingOrganizationsResponse> {
    const params = { page, limit };
    return this.apiService.get<PendingOrganizationsResponse>(
      `${this.path}/pending`,
      params
    );
  }

  /**
   * Get organization by ID or subdomain
   * GET /api/v1/organizations/{idOrSub}
   */
  getOrganizationById(idOrSub: string): Observable<Organization> {
    return this.apiService.get<Organization>(`${this.path}/${idOrSub}`);
  }

  /**
   * Update organization by ID or subdomain
   * PUT /api/v1/organizations/{idOrSub}
   */
  updateOrganization(
    idOrSub: string,
    updateData: UpdateOrganizationRequest
  ): Observable<Organization> {
    return this.apiService.put<Organization>(
      `${this.path}/${idOrSub}`,
      updateData
    );
  }

  /**
   * Delete organization by ID or subdomain
   * DELETE /api/v1/organizations/{idOrSub}
   */
  deleteOrganization(
    idOrSub: string
  ): Observable<OrganizationOperationResponse> {
    return this.apiService.delete<OrganizationOperationResponse>(
      `${this.path}/${idOrSub}`
    );
  }

  /**
   * Add user to organization with RBAC role assignment
   * PATCH /api/v1/organizations/{idOrSubdomain}/add-user
   */
  addUserToOrganization(
    idOrSubdomain: string,
    userData: AddUserToOrganizationRequest
  ): Observable<MemberManagementResponse> {
    return this.apiService.patch<MemberManagementResponse>(
      `${this.path}/${idOrSubdomain}/add-user`,
      userData
    );
  }

  /**
   * Remove user from organization
   * PATCH /api/v1/organizations/{idOrSubdomain}/remove-user
   */
  removeUserFromOrganization(
    idOrSubdomain: string,
    userData: RemoveUserFromOrganizationRequest
  ): Observable<MemberManagementResponse> {
    return this.apiService.patch<MemberManagementResponse>(
      `${this.path}/${idOrSubdomain}/remove-user`,
      userData
    );
  }

  /**
   * Get organization details with member information using flexible identifiers
   * GET /api/v1/organizations/{orgIdOrSubdomain}/details
   */
  getOrganizationDetails(
    orgIdOrSubdomain: string
  ): Observable<OrganizationDetailsResponse> {
    return this.apiService.get<OrganizationDetailsResponse>(
      `${this.path}/${orgIdOrSubdomain}/details`
    );
  }

  /**
   * Create a new organization (Any authenticated user)
   * POST /api/v1/organizations/create
   */
  createOrganizationByUser(
    organizationData: CreateOrganizationRequest
  ): Observable<CreateOrganizationResponse> {
    console.log('OrganisationService.createOrganizationByUser called');
    console.log('API endpoint:', `${this.path}/create`);
    console.log('Request payload:', organizationData);

    return this.apiService
      .post<CreateOrganizationResponse>(`${this.path}/create`, organizationData)
      .pipe(
        tap((response) => {
          console.log(
            'OrganisationService.createOrganizationByUser success response:',
            response
          );
        }),
        catchError((error) => {
          console.error(
            'OrganisationService.createOrganizationByUser error:',
            error
          );
          throw error;
        })
      );
  }

  /**
   * Approve organization (God Super User only)
   * PUT /api/v1/organizations/{id}/approve
   */
  approveOrganization(id: string): Observable<ApproveOrganizationResponse> {
    return this.apiService.put<ApproveOrganizationResponse>(
      `${this.path}/${id}/approve`,
      {}
    );
  }

  /**
   * Manage organization members (Add/Remove/Update roles)
   * PUT /api/v1/organizations/{id}/members
   */
  manageOrganizationMembers(
    id: string,
    memberData: MemberManagementRequest
  ): Observable<MemberManagementResponse> {
    return this.apiService.put<MemberManagementResponse>(
      `${this.path}/${id}/members`,
      memberData
    );
  }
}
