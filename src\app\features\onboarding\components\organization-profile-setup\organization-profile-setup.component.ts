import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { OrganizationDataService } from '../../../../core/services/organization-data/organization-data.service';
import { UserDataService } from '../../../../core/services/user-data/user-data.service';
import { SubdomainService } from '../../../../core/services/subdomain/subdomain.service';
import { AlertType } from '../../../../shared/components/alert/alert.component';
import { TeamInvitation } from '../../../../core/models/organization.model';

interface TeamInvite {
  email: string;
  role: string;
}

@Component({
  selector: 'app-organization-profile-setup',
  templateUrl: './organization-profile-setup.component.html',
  styleUrls: ['./organization-profile-setup.component.css'],
})
export class OrganizationProfileSetupComponent implements OnInit {
  currentStep = 1;
  totalSteps = 2;

  // Forms
  organizationForm!: FormGroup;
  teamForm!: FormGroup;

  loading = false;
  showAlert = false;
  alertType: AlertType = 'error';
  alertMessage: string = '';

  // Organization data
  organizationId: string = '';

  // Team invites
  teamInvites: TeamInvite[] = [];

  // Available roles
  availableRoles = [
    { value: 'member', label: 'Member' },
    { value: 'admin', label: 'Admin' },
    { value: 'manager', label: 'Manager' },
    { value: 'viewer', label: 'Viewer' },
  ];

  // Industry options
  industries = [
    'Technology',
    'Healthcare',
    'Finance',
    'Education',
    'Manufacturing',
    'Retail',
    'Real Estate',
    'Media',
    'Legal',
    'Non-profit',
    'Government',
    'Consulting',
    'Transportation',
    'Energy',
    'Other',
  ];

  // Organization roles
  organizationRoles = [
    {
      value: 'sponsor',
      label: 'Sponsor',
      description:
        'Represent your company, exhibit at events, and capture leads',
    },
    {
      value: 'organizer',
      label: 'Organizer',
      description:
        'Create and manage events, sell tickets, and grow your audience',
    },
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private organizationDataService: OrganizationDataService,
    private userDataService: UserDataService,
    private subdomainService: SubdomainService
  ) {}

  ngOnInit(): void {
    this.initializeForms();
  }

  private initializeForms() {
    this.organizationForm = this.formBuilder.group({
      displayName: ['', Validators.required],
      legalName: [''],
      subdomain: [
        '',
        [Validators.required, Validators.pattern(/^[a-z0-9-]+$/)],
      ],
      logo: [null],
      industry: ['', Validators.required],
      contactEmail: ['', [Validators.required, Validators.email]],
      roles: [[], Validators.required],
    });

    this.teamForm = this.formBuilder.group({
      memberEmail: ['', [Validators.required, Validators.email]],
      memberRole: ['member', Validators.required],
    });
  }

  get subdomainPreview(): string {
    const subdomain =
      this.organizationForm.get('subdomain')?.value || 'yourcompany';
    return `${subdomain}.digimeet.live`;
  }

  onSubdomainChange() {
    const subdomain = this.organizationForm.get('subdomain')?.value;
    if (subdomain) {
      // Convert to lowercase and remove invalid characters
      const cleanSubdomain = subdomain.toLowerCase().replace(/[^a-z0-9-]/g, '');
      this.organizationForm.patchValue({ subdomain: cleanSubdomain });
    }
  }

  onRoleChange(role: string, checked: boolean) {
    const currentRoles = this.organizationForm.get('roles')?.value || [];

    if (checked) {
      if (!currentRoles.includes(role)) {
        currentRoles.push(role);
      }
    } else {
      const index = currentRoles.indexOf(role);
      if (index > -1) {
        currentRoles.splice(index, 1);
      }
    }

    this.organizationForm.patchValue({ roles: currentRoles });
  }

  isRoleSelected(role: string): boolean {
    const currentRoles = this.organizationForm.get('roles')?.value || [];
    return currentRoles.includes(role);
  }

  onFileSelect(event: any) {
    const file = event.target.files[0];
    if (file) {
      // Validate file type and size
      const allowedTypes = ['image/png', 'image/jpeg', 'image/gif'];
      const maxSize = 10 * 1024 * 1024; // 10MB

      if (!allowedTypes.includes(file.type)) {
        this.showErrorAlert('Please select a PNG, JPG, or GIF file.');
        return;
      }

      if (file.size > maxSize) {
        this.showErrorAlert('File size must be less than 10MB.');
        return;
      }

      this.organizationForm.patchValue({ logo: file });
    }
  }

  addTeamInvite() {
    if (this.teamForm.invalid) {
      return;
    }

    const email = this.teamForm.get('memberEmail')?.value;
    const role = this.teamForm.get('memberRole')?.value;

    // Check if email already exists
    if (this.teamInvites.some((invite) => invite.email === email)) {
      this.showErrorAlert('This email is already in the invite list.');
      return;
    }

    this.teamInvites.push({ email, role });
    this.teamForm.reset({ memberEmail: '', memberRole: 'member' });
  }

  removeTeamInvite(index: number) {
    this.teamInvites.splice(index, 1);
  }

  nextStep() {
    if (this.currentStep === 1) {
      if (this.organizationForm.invalid) {
        this.showErrorAlert('Please fill in all required fields.');
        return;
      }
      this.createOrganization();
    }
  }

  previousStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  private createOrganization() {
    this.loading = true;
    this.showAlert = false;

    const formData = this.organizationForm.value;

    // Format data according to POST /api/v1/organizations/create API specification
    const organizationData = {
      name: formData.displayName,
      legalName: formData.legalName || undefined,
      subdomain: formData.subdomain,
      contactEmail: formData.contactEmail,
      industryTag: formData.industry,
      organizationRoles: formData.roles || [],
      branding: {
        logoUrl: formData.logo || '', // Handle logo URL - this would need file upload implementation
        primaryColor: '#336699', // Default color, can be made configurable
      },
      teamInvitations: this.teamInvites.map(
        (invite: TeamInvite): TeamInvitation => ({
          email: invite.email,
          role: invite.role,
        })
      ),
    };

    console.log('Creating organization with data:', organizationData);

    this.organizationDataService
      .createOrganizationByUser(organizationData)
      .subscribe({
        next: (response) => {
          console.log('Organization creation response:', response);
          this.organizationId = response.organization._id;
          this.currentStep = 2;
          this.loading = false;
          this.showSuccessAlert('Organization created successfully!');
        },
        error: (error) => {
          console.error('Organization creation error:', error);
          this.loading = false;
          this.showErrorAlert(
            error.message || 'Failed to create organization. Please try again.'
          );
        },
      });
  }

  completeSetup() {
    this.loading = true;

    // Team invitations are now handled during organization creation
    // Just update user type to organization and complete setup
    this.userDataService.updateProfile({ type: 'organization' }).subscribe({
      next: () => {
        this.loading = false;
        this.showSuccessAlert('Organization setup completed successfully!');
        // Redirect to organization dashboard with proper subdomain handling
        setTimeout(() => {
          this.redirectToOrganizationDashboard();
        }, 1500);
      },
      error: (error) => {
        this.loading = false;
        this.showErrorAlert(
          error.message || 'Failed to complete setup. Please try again.'
        );
      },
    });
  }

  private redirectToOrganizationDashboard() {
    const subdomain = this.organizationForm.get('subdomain')?.value;

    if (subdomain && this.subdomainService.areSubdomainsEnabled()) {
      // Production: Redirect to actual subdomain with token transfer
      console.log('Redirecting to organization subdomain:', subdomain);
      this.subdomainService.redirectToSubdomain(subdomain, '/org-dashboard');
    } else {
      // Development: Use path-based routing on same domain
      console.log(
        'Development mode: Redirecting to org-dashboard on main domain'
      );
      this.router.navigate(['/org-dashboard']);
    }
  }

  private showErrorAlert(message: string) {
    this.alertMessage = message;
    this.alertType = 'error';
    this.showAlert = true;
  }

  private showSuccessAlert(message: string) {
    this.alertMessage = message;
    this.alertType = 'success';
    this.showAlert = true;
  }
}
