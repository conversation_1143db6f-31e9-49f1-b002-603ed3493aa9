import { Component, OnInit } from '@angular/core';
import { FormB<PERSON>er, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { UserDataService } from '../../../../core/services/user-data/user-data.service';
import { AuthService } from '../../../../core/services/auth/auth.service';
import { AlertType } from '../../../../shared/components/alert/alert.component';
import { CompleteUser } from '../../../../core/models/user.model';

@Component({
  selector: 'app-individual-profile-setup',
  templateUrl: './individual-profile-setup.component.html',
  styleUrls: ['./individual-profile-setup.component.css'],
})
export class IndividualProfileSetupComponent implements OnInit {
  profileForm!: FormGroup;
  loading = false;
  showAlert = false;
  alertType: AlertType = 'error';
  alertMessage: string = '';

  // Industry/Function tags
  availableTags = [
    'Technology',
    'Healthcare',
    'Finance',
    'Marketing',
    'Sales',
    'HR',
    'Operations',
    'Product Management',
    'Engineering',
    'Design',
    'Consulting',
    'Education',
    'Media',
    'Real Estate',
    'Legal',
    'Non-profit',
    'Government',
    'Retail',
  ];
  selectedTags: string[] = [];

  // Networking goals
  networkingGoals = [
    'Find new business opportunities',
    'Expand professional network',
    'Learn from industry experts',
    'Share knowledge and expertise',
    'Find potential collaborators',
    'Discover new career opportunities',
    'Stay updated with industry trends',
    'Build brand awareness',
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private userDataService: UserDataService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadExistingUserData();
  }

  private initializeForm() {
    this.profileForm = this.formBuilder.group({
      fullName: ['', Validators.required],
      jobTitle: ['', Validators.required],
      companyName: ['', Validators.required],
      bio: ['', [Validators.required, Validators.maxLength(200)]],
      networkingGoal: ['', Validators.required],
      delegateEmail: ['', Validators.email],
    });
  }

  private loadExistingUserData() {
    // Get current user data
    const currentUser = this.authService.getCurrentUser();
    if (currentUser) {
      // Pre-populate with basic user info
      this.profileForm.patchValue({
        fullName: currentUser.name || '',
      });
    }

    // Load complete user data to get profile information
    this.userDataService.loadCompleteUserData().subscribe({
      next: (completeUser: CompleteUser | null) => {
        if (completeUser && completeUser.profile) {
          const profile = completeUser.profile;

          // Pre-populate form with existing profile data
          this.profileForm.patchValue({
            fullName: profile.fullName || currentUser?.name || '',
            jobTitle: profile.jobTitle || '',
            companyName: profile.companyName || '',
            bio: profile.bio || '',
            networkingGoal: profile.networkingGoal || '',
            delegateEmail: profile.delegateEmail || '',
          });

          // Pre-populate selected tags
          if (profile.industryTags && Array.isArray(profile.industryTags)) {
            this.selectedTags = [...profile.industryTags];
          }
        }
      },
      error: (error) => {
        console.error('Error loading user data:', error);
        // Continue with empty form if there's an error
      },
    });
  }

  get bioCharacterCount(): number {
    return this.profileForm.get('bio')?.value?.length || 0;
  }

  addTag(tag: string) {
    if (this.selectedTags.length < 5 && !this.selectedTags.includes(tag)) {
      this.selectedTags.push(tag);
    }
  }

  removeTag(tag: string) {
    this.selectedTags = this.selectedTags.filter((t) => t !== tag);
  }

  addCustomTag(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
      const input = event.target as HTMLInputElement;
      const tag = input.value.trim();

      if (
        tag &&
        this.selectedTags.length < 5 &&
        !this.selectedTags.includes(tag)
      ) {
        this.selectedTags.push(tag);
        input.value = '';
      }
    }
  }

  onSubmit() {
    if (this.profileForm.invalid || this.selectedTags.length === 0) {
      this.showErrorAlert(
        'Please fill in all required fields and select at least one industry tag.'
      );
      return;
    }

    this.loading = true;
    this.showAlert = false;

    // Get current user data to include in the update
    const currentUser = this.authService.getCurrentUser();

    const profileData = {
      // Include basic user info
      name: currentUser?.name,
      email: currentUser?.email,
      phone_number: currentUser?.phone_number,
      // Include form data
      ...this.profileForm.value,
      // Include additional fields
      type: 'individual',
      industryTags: this.selectedTags,
    };

    // Debug: Log the payload being sent
    console.log('Sending profile update with data:', profileData);

    this.userDataService.updateProfile(profileData).subscribe({
      next: (response) => {
        this.loading = false;
        console.log('Profile update successful, response:', response);

        // Show success message
        this.showSuccessAlert(
          'Profile completed successfully! Redirecting to dashboard...'
        );

        // Force refresh user data and then redirect
        this.userDataService.refreshUserData().subscribe({
          next: (refreshedUser) => {
            console.log('User data refreshed after profile update:', {
              type: refreshedUser?.type,
              hasProfile: !!refreshedUser?.profile,
              profileFields: refreshedUser?.profile
                ? Object.keys(refreshedUser.profile)
                : [],
            });

            // Navigate to dashboard after data is refreshed
            setTimeout(() => {
              this.router.navigate(['/dashboard']);
            }, 500);
          },
          error: (refreshError) => {
            console.error('Error refreshing user data:', refreshError);
            // Still redirect even if refresh fails
            setTimeout(() => {
              this.router.navigate(['/dashboard']);
            }, 500);
          },
        });
      },
      error: (error) => {
        this.loading = false;
        console.error('Profile update error:', error);
        console.error('Error details:', {
          message: error.message,
          status: error.status,
          statusText: error.statusText,
          error: error.error,
        });
        this.showErrorAlert(
          error.message || 'Failed to update profile. Please try again.'
        );
      },
    });
  }

  private showErrorAlert(message: string) {
    this.alertMessage = message;
    this.alertType = 'error';
    this.showAlert = true;
  }

  private showSuccessAlert(message: string) {
    this.alertMessage = message;
    this.alertType = 'success';
    this.showAlert = true;
  }
}
