import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth/auth.service';
import { UserDataService } from '../services/user-data/user-data.service';
import { SubdomainService } from '../services/subdomain/subdomain.service';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class SubdomainValidationGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private userDataService: UserDataService,
    private router: Router,
    private subdomainService: SubdomainService
  ) {}

  canActivate(route: any): Observable<boolean> {
    console.log('SubdomainValidationGuard: Validating subdomain access...');
    console.log(
      'SubdomainValidationGuard: Route path:',
      route?.routeConfig?.path
    );

    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      console.log(
        'SubdomainValidationGuard: User not authenticated, redirecting to login'
      );
      this.router.navigate(['/auth/login']);
      return of(false);
    }

    // Only validate subdomains in production or when subdomains are enabled
    if (!this.subdomainService.areSubdomainsEnabled()) {
      console.log(
        'SubdomainValidationGuard: Subdomains disabled, allowing access'
      );
      return of(true);
    }

    const currentSubdomain = this.subdomainService.getCurrentSubdomain();
    const routePath = route?.routeConfig?.path;

    // If no subdomain detected, this might be main domain access
    if (!currentSubdomain) {
      console.log(
        'SubdomainValidationGuard: No subdomain detected, checking if user should be redirected'
      );
      return this.checkAndRedirectToUserSubdomain();
    }

    // CRITICAL: If on a subdomain, validate access regardless of route
    console.log(
      'SubdomainValidationGuard: On subdomain, validating access to:',
      currentSubdomain,
      'for route:',
      routePath
    );

    return this.validateUserSubdomainAccess(currentSubdomain, routePath);
  }

  private checkAndRedirectToUserSubdomain(): Observable<boolean> {
    return this.userDataService.refreshUserData().pipe(
      map((completeUser) => {
        if (!completeUser) {
          console.log(
            'SubdomainValidationGuard: No user data, redirecting to login'
          );
          this.router.navigate(['/auth/login']);
          return false;
        }

        // If user is organization type, redirect to their subdomain
        if (completeUser.type === 'organization') {
          // Prioritize default organization, then fall back to first organization
          let targetOrg = completeUser.defaultOrganization;

          if (
            !targetOrg &&
            completeUser.organizations &&
            completeUser.organizations.length > 0
          ) {
            targetOrg = completeUser.organizations[0];
          }

          if (targetOrg && targetOrg.subdomain) {
            console.log(
              'SubdomainValidationGuard: Redirecting to user subdomain:',
              targetOrg.subdomain,
              '(from',
              completeUser.defaultOrganization ? 'default org' : 'first org',
              ')'
            );
            this.subdomainService.redirectToSubdomain(
              targetOrg.subdomain,
              '/org-dashboard'
            );
            return false;
          } else {
            console.log(
              'SubdomainValidationGuard: Organization user has no valid subdomain'
            );
          }
        }

        // Individual users on main domain should be allowed to access dashboard
        if (completeUser.type === 'individual') {
          console.log(
            'SubdomainValidationGuard: Individual user on main domain, allowing access to dashboard'
          );
          return true;
        }

        // Fallback for other cases
        console.log(
          'SubdomainValidationGuard: Unknown user type or no organization, allowing access'
        );
        return true;
      }),
      catchError((error) => {
        console.error(
          'SubdomainValidationGuard: Error checking user data:',
          error
        );
        this.router.navigate(['/auth/login']);
        return of(false);
      })
    );
  }

  private validateUserSubdomainAccess(
    subdomain: string,
    routePath?: string
  ): Observable<boolean> {
    // First try to get user data from localStorage (faster)
    const cachedUser = this.authService.getCompleteUser();

    if (cachedUser) {
      console.log(
        'SubdomainValidationGuard: Using cached user data for validation'
      );
      const isValid = this.validateSubdomainAccess(
        cachedUser,
        subdomain,
        routePath
      );
      if (isValid !== null) {
        return of(isValid);
      }
    }

    // If no cached data or validation inconclusive, try to fetch from server
    // But use the smart loading method that handles CORS gracefully
    console.log(
      'SubdomainValidationGuard: Fetching fresh user data from server'
    );
    return this.userDataService.loadCompleteUserData().pipe(
      map((completeUser) => {
        if (!completeUser) {
          console.log(
            'SubdomainValidationGuard: No user data available, access denied'
          );
          this.router.navigate(['/auth/login']);
          return false;
        }

        const isValid = this.validateSubdomainAccess(
          completeUser,
          subdomain,
          routePath
        );
        return isValid !== null ? isValid : false;
      }),
      catchError((error) => {
        console.error(
          'SubdomainValidationGuard: Error fetching user data:',
          error
        );

        // If we have cached data and this is a CORS error, try to use cached data
        if (cachedUser && this.isCorsError(error)) {
          console.log(
            'SubdomainValidationGuard: CORS error detected, falling back to cached data'
          );
          const isValid = this.validateSubdomainAccess(
            cachedUser,
            subdomain,
            routePath
          );
          return of(isValid !== null ? isValid : false);
        }

        // Otherwise redirect to login
        this.router.navigate(['/auth/login']);
        return of(false);
      })
    );
  }

  private isCorsError(error: any): boolean {
    return (
      error &&
      (error.message?.includes('CORS') ||
        error.message?.includes('Access-Control-Allow-Origin') ||
        error.status === 0 ||
        (error.error instanceof ProgressEvent && error.status === 0))
    );
  }

  private validateSubdomainAccess(
    completeUser: any,
    subdomain: string,
    routePath?: string
  ): boolean | null {
    console.log(
      'SubdomainValidationGuard: Validating access to subdomain:',
      subdomain,
      'for user:',
      completeUser.email
    );

    // Check if user is organization type
    if (completeUser.type !== 'organization') {
      console.log(
        'SubdomainValidationGuard: Individual user trying to access organization subdomain'
      );
      // If individual user is on a subdomain, redirect to 404 (they shouldn't be here)
      console.log(
        'SubdomainValidationGuard: Individual user on subdomain - redirecting to 404'
      );
      this.router.navigate(['/404']);
      return false;
    }

    // Get user's organizations
    const userOrgs = completeUser.organizations || [];
    console.log(
      'SubdomainValidationGuard: User organizations:',
      userOrgs.map((org: any) => ({ name: org.name, subdomain: org.subdomain }))
    );

    // Check if the requested subdomain exists in user's organizations
    const hasAccess = userOrgs.some((org: any) => org.subdomain === subdomain);

    if (!hasAccess) {
      console.log(
        'SubdomainValidationGuard: 🚨 UNAUTHORIZED ACCESS ATTEMPT 🚨'
      );
      console.log(
        'SubdomainValidationGuard: User does not have access to subdomain:',
        subdomain
      );
      console.log(
        'SubdomainValidationGuard: Available subdomains:',
        userOrgs.map((org: any) => org.subdomain)
      );
      console.log('SubdomainValidationGuard: Route path:', routePath);

      // Block access completely - redirect to 404 for unauthorized subdomain access
      console.log(
        'SubdomainValidationGuard: 🚫 BLOCKING ACCESS - Redirecting to 404'
      );
      this.router.navigate(['/404']);
      return false;
    }

    console.log(
      'SubdomainValidationGuard: ✅ User has valid access to subdomain:',
      subdomain
    );

    // Special case: If user is accessing /dashboard on a valid subdomain, redirect to /org-dashboard
    if (routePath === 'dashboard') {
      console.log(
        'SubdomainValidationGuard: User accessing /dashboard on subdomain, redirecting to /org-dashboard'
      );
      this.router.navigate(['/org-dashboard']);
      return false;
    }

    return true;
  }
}
