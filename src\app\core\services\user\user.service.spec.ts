import { TestBed } from '@angular/core/testing';
import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { environment } from '../../../../environments/environment';

import { UserService } from './user.service';
import { ApiService } from '../api/api.service';
import {
  User,
  CompleteUser,
  PaginatedUsersResponse,
  UserProfileUpdateRequest,
  UserProfileUpdateResponse,
  UserStatusUpdateRequest,
  UserStatusUpdateResponse,
  UserStatusResponse,
  DeleteUserResponse,
} from '../../models/user.model';

describe('UserService', () => {
  let service: UserService;
  let httpMock: HttpTestingController;
  let apiService: ApiService;

  const mockUser: User = {
    _id: '123',
    JWT_UID: 'jwt123',
    email: '<EMAIL>',
    name: 'Test User',
    phone_number: '+1234567890',
    roles: [],
  };

  const mockCompleteUser: CompleteUser = {
    _id: '123',
    name: 'Test User',
    email: '<EMAIL>',
    phone_number: '+1234567890',
    type: 'individual',
    status: 'active',
    profile: {
      fullName: 'Test User Full',
      jobTitle: 'Developer',
      companyName: 'Test Corp',
      bio: 'Test bio',
    },
    roles: [],
    systemPrivileges: [],
    organizations: [],
    privilegeSummary: {
      isGodSuperUser: false,
      hasSystemPrivileges: false,
      highestPrivilegeLevel: 0,
      organizationCount: 0,
    },
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [UserService, ApiService],
    });

    service = TestBed.inject(UserService);
    httpMock = TestBed.inject(HttpTestingController);
    apiService = TestBed.inject(ApiService);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAllUsers', () => {
    it('should get all users with pagination', () => {
      const mockResponse: PaginatedUsersResponse = {
        page: 1,
        limit: 10,
        total: 100,
        users: [
          { email: '<EMAIL>', name: 'User 1' },
          { email: '<EMAIL>', name: 'User 2' },
        ],
      };

      service.getAllUsers(1, 10).subscribe((response) => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/users?page=1&limit=10`
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should use default pagination parameters', () => {
      const mockResponse: PaginatedUsersResponse = {
        page: 1,
        limit: 10,
        total: 0,
        users: [],
      };

      service.getAllUsers().subscribe();

      const req = httpMock.expectOne(
        `${environment.apiUrl}/users?page=1&limit=10`
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getCurrentUserComplete', () => {
    it('should get complete user profile', () => {
      service.getCurrentUserComplete().subscribe((user) => {
        expect(user).toEqual(mockCompleteUser);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/me`);
      expect(req.request.method).toBe('GET');
      req.flush(mockCompleteUser);
    });
  });

  describe('updateCurrentUser', () => {
    it('should update current user profile', () => {
      const updateRequest: UserProfileUpdateRequest = {
        name: 'Updated Name',
        email: '<EMAIL>',
        bio: 'Updated bio',
      };

      const updateResponse: UserProfileUpdateResponse = {
        message: 'Profile updated successfully',
        user: mockCompleteUser,
      };

      service.updateCurrentUser(updateRequest).subscribe((response) => {
        expect(response).toEqual(updateResponse);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/update_user`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateRequest);
      req.flush(updateResponse);
    });
  });

  describe('deleteCurrentUser', () => {
    it('should delete current user', () => {
      const deleteResponse: DeleteUserResponse = {
        message: 'User deleted successfully',
      };

      service.deleteCurrentUser().subscribe((response) => {
        expect(response).toEqual(deleteResponse);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/delete_user`);
      expect(req.request.method).toBe('DELETE');
      req.flush(deleteResponse);
    });
  });

  describe('updateUserStatus', () => {
    it('should update user status', () => {
      const statusRequest: UserStatusUpdateRequest = {
        identifier: '<EMAIL>',
        status: 'inactive',
      };

      const statusResponse: UserStatusUpdateResponse = {
        message: 'Status updated successfully',
        user: {
          _id: '123',
          email: '<EMAIL>',
          status: 'inactive',
        },
      };

      service.updateUserStatus(statusRequest).subscribe((response) => {
        expect(response).toEqual(statusResponse);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/status`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(statusRequest);
      req.flush(statusResponse);
    });
  });

  describe('getUserStatus', () => {
    it('should get user status by identifier', () => {
      const statusResponse: UserStatusResponse = {
        _id: '123',
        email: '<EMAIL>',
        status: 'active',
      };

      service.getUserStatus('<EMAIL>').subscribe((response) => {
        expect(response).toEqual(statusResponse);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/users/status?identifier=test%40example.com`
      );
      expect(req.request.method).toBe('GET');
      req.flush(statusResponse);
    });
  });

  // Legacy method tests
  describe('getUsers (legacy)', () => {
    it('should get users with custom parameters', () => {
      const params = { role: 'admin', status: 'active' };

      service.getUsers(params).subscribe((users) => {
        expect(users).toEqual([mockUser]);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/users?role=admin&status=active`
      );
      expect(req.request.method).toBe('GET');
      req.flush([mockUser]);
    });
  });

  describe('getUserById', () => {
    it('should get user by ID', () => {
      service.getUserById('123').subscribe((user) => {
        expect(user).toEqual(mockUser);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/123`);
      expect(req.request.method).toBe('GET');
      req.flush(mockUser);
    });
  });

  describe('getCurrentUserProfile (legacy)', () => {
    it('should get current user profile', () => {
      service.getCurrentUserProfile().subscribe((user) => {
        expect(user).toEqual(mockUser);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/me`);
      expect(req.request.method).toBe('GET');
      req.flush(mockUser);
    });
  });

  describe('updateUser', () => {
    it('should update user by ID', () => {
      const updateData = { name: 'Updated Name' };

      service.updateUser('123', updateData).subscribe((user) => {
        expect(user).toEqual(mockUser);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/123`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockUser);
    });
  });

  describe('updateProfile (legacy)', () => {
    it('should update current user profile', () => {
      const updateData = { name: 'Updated Name' };

      service.updateProfile(updateData).subscribe((user) => {
        expect(user).toEqual(mockUser);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/me`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockUser);
    });
  });

  describe('deleteUser', () => {
    it('should delete user by ID', () => {
      service.deleteUser('123').subscribe();

      const req = httpMock.expectOne(`${environment.apiUrl}/users/123`);
      expect(req.request.method).toBe('DELETE');
      req.flush({});
    });
  });
});
