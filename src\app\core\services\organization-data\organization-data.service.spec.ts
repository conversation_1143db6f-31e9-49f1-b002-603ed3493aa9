import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';

import { OrganizationDataService } from './organization-data.service';
import { OrganisationService } from '../organisation/organisation.service';
import { UserDataService } from '../user-data/user-data.service';
import {
  Organization,
  CreateOrganizationRequest,
  UpdateOrganizationRequest,
  CreateOrganizationResponse,
  OrganizationDetailsResponse,
  OrganizationOperationResponse,
} from '../../models/organization.model';

describe('OrganizationDataService', () => {
  let service: OrganizationDataService;
  let organizationServiceSpy: jasmine.SpyObj<OrganisationService>;
  let userDataServiceSpy: jasmine.SpyObj<UserDataService>;

  const mockOrganization: Organization = {
    _id: '663041cf7a14c7c000a3f999',
    name: 'Acme Corporation',
    subdomain: 'acme-corp',
    status: 'active',
    branding: {
      logoUrl: 'https://acme.com/logo.png',
      primaryColor: '#336699',
    },
    createdAt: new Date('2025-06-02T17:35:17.117Z'),
    updatedAt: new Date('2025-06-02T17:35:17.117Z'),
  };

  const mockCreateRequest: CreateOrganizationRequest = {
    name: 'Acme Corporation',
    legalName: 'Acme Corporation LLC',
    subdomain: 'acme-corp',
    contactEmail: '<EMAIL>',
    industryTag: 'Technology',
    organizationRoles: ['sponsor', 'organizer'],
    branding: {
      logoUrl: 'https://acme.com/logo.png',
      primaryColor: '#336699',
    },
    teamInvitations: [
      {
        email: '<EMAIL>',
        role: 'member',
      },
    ],
  };

  const mockPermissions = {
    isAdmin: true,
    isGodSuperUser: false,
    hasSystemPrivileges: false,
    organizationCount: 1,
    roles: ['admin'],
  };

  beforeEach(() => {
    const orgSpy = jasmine.createSpyObj('OrganisationService', [
      'createOrganization',
      'createOrganizationByUser',
      'updateOrganization',
      'deleteOrganization',
      'getOrganizationDetails',
      'getOrganizationById',
    ]);

    const userDataSpy = jasmine.createSpyObj('UserDataService', [
      'getUserPermissions',
      'getUserOrganizations',
    ]);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        OrganizationDataService,
        { provide: OrganisationService, useValue: orgSpy },
        { provide: UserDataService, useValue: userDataSpy },
      ],
    });

    service = TestBed.inject(OrganizationDataService);
    organizationServiceSpy = TestBed.inject(
      OrganisationService
    ) as jasmine.SpyObj<OrganisationService>;
    userDataServiceSpy = TestBed.inject(
      UserDataService
    ) as jasmine.SpyObj<UserDataService>;

    userDataServiceSpy.getUserPermissions.and.returnValue(of(mockPermissions));
    userDataServiceSpy.getUserOrganizations.and.returnValue(
      of([mockOrganization])
    );
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getCurrentOrganization', () => {
    it('should return current organization', () => {
      service.setCurrentOrganization(mockOrganization);

      const result = service.getCurrentOrganization();

      expect(result).toEqual(mockOrganization);
    });

    it('should return null when no organization is set', () => {
      const result = service.getCurrentOrganization();

      expect(result).toBeNull();
    });
  });

  describe('setCurrentOrganization', () => {
    it('should set current organization', () => {
      service.setCurrentOrganization(mockOrganization);

      service.currentOrganization$.subscribe((org) => {
        expect(org).toEqual(mockOrganization);
      });
    });
  });

  describe('isOrganizationAdmin', () => {
    it('should return true when user has admin role', () => {
      service.isOrganizationAdmin().subscribe((isAdmin) => {
        expect(isAdmin).toBe(true);
      });
    });

    it('should return false when user has no admin role', () => {
      const nonAdminPermissions = { ...mockPermissions, roles: ['user'] };
      userDataServiceSpy.getUserPermissions.and.returnValue(
        of(nonAdminPermissions)
      );

      service.isOrganizationAdmin().subscribe((isAdmin) => {
        expect(isAdmin).toBe(false);
      });
    });

    it('should return false when permissions are null', () => {
      userDataServiceSpy.getUserPermissions.and.returnValue(of(null));

      service.isOrganizationAdmin().subscribe((isAdmin) => {
        expect(isAdmin).toBe(false);
      });
    });
  });

  describe('isGodSuperUser', () => {
    it('should return true when user is God Super User', () => {
      const godUserPermissions = { ...mockPermissions, isGodSuperUser: true };
      userDataServiceSpy.getUserPermissions.and.returnValue(
        of(godUserPermissions)
      );

      service.isGodSuperUser().subscribe((isGod) => {
        expect(isGod).toBe(true);
      });
    });

    it('should return false when user is not God Super User', () => {
      service.isGodSuperUser().subscribe((isGod) => {
        expect(isGod).toBe(false);
      });
    });
  });

  describe('createOrganization', () => {
    it('should create organization and set as current', () => {
      const mockResponse: CreateOrganizationResponse = {
        success: true,
        message: 'Organization created successfully',
        organization: mockOrganization,
      };

      organizationServiceSpy.createOrganization.and.returnValue(
        of(mockResponse)
      );

      service.createOrganization(mockCreateRequest).subscribe((response) => {
        expect(response).toEqual(mockResponse);
        expect(service.getCurrentOrganization()).toEqual(mockOrganization);
      });

      expect(organizationServiceSpy.createOrganization).toHaveBeenCalledWith(
        mockCreateRequest
      );
    });

    it('should handle errors and update loading state', () => {
      const error = new Error('Creation failed');
      organizationServiceSpy.createOrganization.and.returnValue(
        throwError(() => error)
      );

      service.createOrganization(mockCreateRequest).subscribe({
        error: (err) => {
          expect(err).toEqual(error);
        },
      });
    });
  });

  describe('updateOrganization', () => {
    it('should update organization and set as current', () => {
      const updateData: UpdateOrganizationRequest = {
        name: 'Updated Acme Corporation',
      };

      organizationServiceSpy.updateOrganization.and.returnValue(
        of(mockOrganization)
      );

      service
        .updateOrganization('acme-corp', updateData)
        .subscribe((organization) => {
          expect(organization).toEqual(mockOrganization);
          expect(service.getCurrentOrganization()).toEqual(mockOrganization);
        });

      expect(organizationServiceSpy.updateOrganization).toHaveBeenCalledWith(
        'acme-corp',
        updateData
      );
    });
  });

  describe('deleteOrganization', () => {
    it('should delete organization and clear current', () => {
      const mockResponse: OrganizationOperationResponse = {
        success: true,
        message: 'Organization deleted successfully',
      };

      service.setCurrentOrganization(mockOrganization);
      organizationServiceSpy.deleteOrganization.and.returnValue(
        of(mockResponse)
      );

      service.deleteOrganization('acme-corp').subscribe((response) => {
        expect(response).toEqual(mockResponse);
        expect(service.getCurrentOrganization()).toBeNull();
      });

      expect(organizationServiceSpy.deleteOrganization).toHaveBeenCalledWith(
        'acme-corp'
      );
    });
  });

  describe('loadOrganizationDetails', () => {
    it('should load organization details and set as current', () => {
      const mockResponse: OrganizationDetailsResponse = {
        success: true,
        data: {
          ...mockOrganization,
          members: [],
        },
        metadata: {
          lookup_method: 'objectId',
          member_count: 0,
          retrieved_at: new Date(),
        },
      };

      organizationServiceSpy.getOrganizationDetails.and.returnValue(
        of(mockResponse)
      );

      service.loadOrganizationDetails('acme-corp').subscribe((response) => {
        expect(response).toEqual(mockResponse);
        expect(service.getCurrentOrganization()).toEqual(mockResponse.data);
      });

      expect(
        organizationServiceSpy.getOrganizationDetails
      ).toHaveBeenCalledWith('acme-corp');
    });
  });

  describe('canManageOrganization', () => {
    it('should return true for God Super User', () => {
      const godUserPermissions = { ...mockPermissions, isGodSuperUser: true };
      userDataServiceSpy.getUserPermissions.and.returnValue(
        of(godUserPermissions)
      );

      service.canManageOrganization('org123').subscribe((canManage) => {
        expect(canManage).toBe(true);
      });
    });

    it('should return true for organization admin', () => {
      service.canManageOrganization('org123').subscribe((canManage) => {
        expect(canManage).toBe(true);
      });
    });

    it('should return false for regular user', () => {
      const regularUserPermissions = {
        ...mockPermissions,
        roles: ['user'],
        isGodSuperUser: false,
      };
      userDataServiceSpy.getUserPermissions.and.returnValue(
        of(regularUserPermissions)
      );

      service.canManageOrganization('org123').subscribe((canManage) => {
        expect(canManage).toBe(false);
      });
    });
  });

  describe('organizationApi getter', () => {
    it('should return OrganisationService instance', () => {
      const orgApi = service.organizationApi;

      expect(orgApi).toBe(organizationServiceSpy);
    });
  });

  describe('refreshCurrentOrganization', () => {
    it('should refresh current organization data', () => {
      service.setCurrentOrganization(mockOrganization);
      organizationServiceSpy.getOrganizationById.and.returnValue(
        of(mockOrganization)
      );

      service.refreshCurrentOrganization().subscribe((organization) => {
        expect(organization).toEqual(mockOrganization);
      });

      expect(organizationServiceSpy.getOrganizationById).toHaveBeenCalledWith(
        mockOrganization._id
      );
    });

    it('should return null when no current organization', () => {
      service.refreshCurrentOrganization().subscribe((organization) => {
        expect(organization).toBeNull();
      });
    });
  });

  describe('initializeOrganizationData', () => {
    it('should set first organization as current', () => {
      service.initializeOrganizationData().subscribe((organization) => {
        expect(organization).toEqual(mockOrganization);
        expect(service.getCurrentOrganization()).toEqual(mockOrganization);
      });
    });

    it('should return null when user has no organizations', () => {
      userDataServiceSpy.getUserOrganizations.and.returnValue(of([]));

      service.initializeOrganizationData().subscribe((organization) => {
        expect(organization).toBeNull();
      });
    });

    it('should handle errors gracefully', () => {
      userDataServiceSpy.getUserOrganizations.and.returnValue(
        throwError(() => new Error('Failed'))
      );
      spyOn(console, 'warn');

      service.initializeOrganizationData().subscribe((organization) => {
        expect(organization).toBeNull();
        expect(console.warn).toHaveBeenCalled();
      });
    });
  });
});
