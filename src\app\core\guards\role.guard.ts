import { Injectable } from '@angular/core';
import {
  Router,
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
} from '@angular/router';
import { AuthService } from '../services/auth/auth.service';
import { Role } from '../models/role.enum';

@Injectable({
  providedIn: 'root',
})
export class RoleGuard implements CanActivate {
  constructor(private router: Router, private authService: AuthService) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    const user = this.authService.getCurrentUser();

    if (!user) {
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Check if route has data.roles specified
    const requiredRoles = route.data['roles'] as Role[];

    if (!requiredRoles || requiredRoles.length === 0) {
      return true; // No specific roles required
    }

    // Check if user has at least one of the required roles
    // If user has no roles defined, they don't have any required roles
    if (!user.roles || user.roles.length === 0) {
      this.router.navigate(['/dashboard']);
      return false;
    }

    // Check if user has at least one of the required roles
    const hasRequiredRole = requiredRoles.some((role) =>
      user.roles!.includes(role)
    );

    if (hasRequiredRole) {
      return true;
    }

    // User doesn't have the required role, redirect to dashboard
    this.router.navigate(['/dashboard']);
    return false;
  }
}
