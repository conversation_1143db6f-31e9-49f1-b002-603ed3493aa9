<div class="user-profile-container" *ngIf="completeUser">
  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="loading-indicator">
    <div class="spinner"></div>
    <p>Loading user data...</p>
  </div>

  <!-- User Profile Content -->
  <div class="profile-content" [class.loading]="isLoading">
    <!-- Basic Info Section -->
    <div class="profile-section">
      <h2>Basic Information</h2>
      <div class="info-grid">
        <div class="info-item">
          <label>Name:</label>
          <span>{{ userName }}</span>
        </div>
        <div class="info-item">
          <label>Email:</label>
          <span>{{ userEmail }}</span>
        </div>
        <div class="info-item">
          <label>Phone:</label>
          <span>{{ completeUser.phone_number || 'Not provided' }}</span>
        </div>
        <div class="info-item">
          <label>Type:</label>
          <span>{{ completeUser.type | titlecase }}</span>
        </div>
        <div class="info-item">
          <label>Status:</label>
          <span class="status" [class]="completeUser.status">{{ completeUser.status | titlecase }}</span>
        </div>
      </div>
    </div>

    <!-- Extended Profile Section -->
    <div class="profile-section" *ngIf="completeUser.profile">
      <h2>Profile Details</h2>
      <div class="info-grid">
        <div class="info-item" *ngIf="completeUser.profile.fullName">
          <label>Full Name:</label>
          <span>{{ completeUser.profile.fullName }}</span>
        </div>
        <div class="info-item" *ngIf="completeUser.profile.jobTitle">
          <label>Job Title:</label>
          <span>{{ completeUser.profile.jobTitle }}</span>
        </div>
        <div class="info-item" *ngIf="completeUser.profile.companyName">
          <label>Company:</label>
          <span>{{ completeUser.profile.companyName }}</span>
        </div>
        <div class="info-item full-width" *ngIf="completeUser.profile.bio">
          <label>Bio:</label>
          <p>{{ completeUser.profile.bio }}</p>
        </div>
        <div class="info-item" *ngIf="completeUser.profile.timezone">
          <label>Timezone:</label>
          <span>{{ completeUser.profile.timezone }}</span>
        </div>
        <div class="info-item" *ngIf="completeUser.profile.language">
          <label>Language:</label>
          <span>{{ completeUser.profile.language }}</span>
        </div>
      </div>

      <!-- Industry Tags -->
      <div class="tags-section" *ngIf="completeUser.profile.industryTags?.length">
        <label>Industry Tags:</label>
        <div class="tags">
          <span class="tag" *ngFor="let tag of completeUser.profile.industryTags">{{ tag }}</span>
        </div>
      </div>

      <!-- Networking Goal -->
      <div class="info-item full-width" *ngIf="completeUser.profile.networkingGoal">
        <label>Networking Goal:</label>
        <p>{{ completeUser.profile.networkingGoal }}</p>
      </div>
    </div>

    <!-- Organizations Section -->
    <div class="profile-section" *ngIf="userOrganizations.length">
      <h2>Organizations</h2>
      <div class="organizations-list">
        <div class="organization-card" *ngFor="let org of userOrganizations">
          <h3>{{ org.name }}</h3>
          <p>Subdomain: {{ org.subdomain }}</p>
          <div class="branding" *ngIf="org.branding">
            <img *ngIf="org.branding.logoUrl" [src]="org.branding.logoUrl" alt="Logo" class="org-logo">
            <span *ngIf="org.branding.primaryColor" class="color-indicator" 
                  [style.background-color]="org.branding.primaryColor"></span>
          </div>
        </div>
      </div>
    </div>

    <!-- Roles and Permissions Section -->
    <div class="profile-section" *ngIf="userPermissions">
      <h2>Roles & Permissions</h2>
      <div class="permissions-grid">
        <div class="permission-item">
          <label>Admin Status:</label>
          <span class="badge" [class.admin]="isAdmin">{{ isAdmin ? 'Admin' : 'User' }}</span>
        </div>
        <div class="permission-item">
          <label>Super User:</label>
          <span class="badge" [class.super]="userPermissions.isGodSuperUser">
            {{ userPermissions.isGodSuperUser ? 'Yes' : 'No' }}
          </span>
        </div>
        <div class="permission-item">
          <label>System Privileges:</label>
          <span class="badge" [class.privileged]="userPermissions.hasSystemPrivileges">
            {{ userPermissions.hasSystemPrivileges ? 'Yes' : 'No' }}
          </span>
        </div>
        <div class="permission-item">
          <label>Organization Count:</label>
          <span>{{ userPermissions.organizationCount }}</span>
        </div>
      </div>

      <!-- Roles List -->
      <div class="roles-section" *ngIf="userRoles.length">
        <label>Roles:</label>
        <div class="roles">
          <span class="role-badge" *ngFor="let role of userRoles">{{ role }}</span>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="actions-section">
      <button class="btn btn-primary" (click)="refreshUserData()" [disabled]="isLoading">
        {{ isLoading ? 'Refreshing...' : 'Refresh Data' }}
      </button>
      <button class="btn btn-secondary" (click)="updateProfile()" [disabled]="isLoading">
        Update Profile
      </button>
      <button class="btn btn-danger" (click)="deleteAccount()" [disabled]="isLoading">
        Delete Account
      </button>
    </div>
  </div>
</div>

<!-- No User Data -->
<div class="no-user-data" *ngIf="!completeUser && !isLoading">
  <h2>No User Data Available</h2>
  <p>Please log in to view your profile information.</p>
  <button class="btn btn-primary" (click)="refreshUserData()">
    Try Loading Data
  </button>
</div>
