import { TestBed } from '@angular/core/testing';
import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { environment } from '../../../../environments/environment';

import { OrganisationService } from './organisation.service';
import { ApiService } from '../api/api.service';
import {
  Organization,
  CreateOrganizationRequest,
  UpdateOrganizationRequest,
  PaginatedOrganizationsResponse,
  PendingOrganizationsResponse,
  OrganizationDetailsResponse,
  AddUserToOrganizationRequest,
  RemoveUserFromOrganizationRequest,
  MemberManagementRequest,
  CreateOrganizationResponse,
  ApproveOrganizationResponse,
  MemberManagementResponse,
  OrganizationOperationResponse,
} from '../../models/organization.model';

describe('OrganisationService', () => {
  let service: OrganisationService;
  let httpMock: HttpTestingController;
  let apiService: ApiService;

  const mockOrganization: Organization = {
    _id: '663041cf7a14c7c000a3f999',
    name: 'Acme Corporation',
    subdomain: 'acme-corp',
    status: 'active',
    branding: {
      logoUrl: 'https://acme.com/logo.png',
      primaryColor: '#336699',
    },
    createdAt: new Date('2025-06-02T17:35:17.117Z'),
    updatedAt: new Date('2025-06-02T17:35:17.117Z'),
  };

  const mockCreateRequest: CreateOrganizationRequest = {
    name: 'Acme Corporation',
    legalName: 'Acme Corporation LLC',
    subdomain: 'acme-corp',
    contactEmail: '<EMAIL>',
    industryTag: 'Technology',
    organizationRoles: ['sponsor', 'organizer'],
    branding: {
      logoUrl: 'https://acme.com/logo.png',
      primaryColor: '#336699',
    },
    teamInvitations: [
      {
        email: '<EMAIL>',
        role: 'member',
      },
    ],
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [OrganisationService, ApiService],
    });

    service = TestBed.inject(OrganisationService);
    httpMock = TestBed.inject(HttpTestingController);
    apiService = TestBed.inject(ApiService);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('createOrganization', () => {
    it('should create a new organization', () => {
      const mockResponse: CreateOrganizationResponse = {
        success: true,
        message: 'Organization created successfully',
        organization: mockOrganization,
      };

      service.createOrganization(mockCreateRequest).subscribe((response) => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/organizations`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockCreateRequest);
      req.flush(mockResponse);
    });
  });

  describe('getAllOrganizations', () => {
    it('should get all organizations with pagination', () => {
      const mockResponse: PaginatedOrganizationsResponse = {
        page: 1,
        limit: 10,
        total: 100,
        organizations: [mockOrganization],
      };

      service.getAllOrganizations(1, 10).subscribe((response) => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations?page=1&limit=10`
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should use default pagination parameters', () => {
      const mockResponse: PaginatedOrganizationsResponse = {
        page: 1,
        limit: 10,
        total: 0,
        organizations: [],
      };

      service.getAllOrganizations().subscribe();

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations?page=1&limit=10`
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getPendingOrganizations', () => {
    it('should get pending organizations (God Super User only)', () => {
      const mockResponse: PendingOrganizationsResponse = {
        success: true,
        data: {
          organizations: [
            {
              _id: '6838bfd6278ccc88e4179049',
              name: 'Acme Corporation',
              subdomain: 'acme-corp',
              branding: {
                logoUrl: 'https://acme.com/logo.png',
                primaryColor: '#336699',
              },
              createdAt: new Date('2025-05-29T20:13:10.956Z'),
              createdBy: {
                _id: '68330577e2576bff416a2ae7',
                name: 'John Doe',
                email: '<EMAIL>',
              },
              memberCount: 1,
            },
          ],
          pagination: {
            page: 1,
            limit: 10,
            total: 25,
            pages: 3,
            hasNext: true,
            hasPrev: false,
          },
        },
        metadata: {
          retrieved_at: new Date('2025-05-29T20:20:52.975Z'),
          duration_ms: 57,
          privilege_level: 'god_super_user',
        },
      };

      service.getPendingOrganizations(1, 10).subscribe((response) => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/pending?page=1&limit=10`
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getOrganizationById', () => {
    it('should get organization by ID', () => {
      const orgId = '663041cf7a14c7c000a3f999';

      service.getOrganizationById(orgId).subscribe((organization) => {
        expect(organization).toEqual(mockOrganization);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/${orgId}`
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockOrganization);
    });

    it('should get organization by subdomain', () => {
      const subdomain = 'acme-corp';

      service.getOrganizationById(subdomain).subscribe((organization) => {
        expect(organization).toEqual(mockOrganization);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/${subdomain}`
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockOrganization);
    });
  });

  describe('updateOrganization', () => {
    it('should update organization by ID', () => {
      const orgId = '663041cf7a14c7c000a3f999';
      const updateData: UpdateOrganizationRequest = {
        name: 'Updated Acme Corporation',
        branding: {
          logoUrl: 'https://acme.com/new-logo.png',
          primaryColor: '#ff6600',
        },
      };

      service
        .updateOrganization(orgId, updateData)
        .subscribe((organization) => {
          expect(organization).toEqual(mockOrganization);
        });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/${orgId}`
      );
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockOrganization);
    });
  });

  describe('deleteOrganization', () => {
    it('should delete organization by ID', () => {
      const orgId = '663041cf7a14c7c000a3f999';
      const mockResponse: OrganizationOperationResponse = {
        success: true,
        message: 'Organization deleted successfully',
      };

      service.deleteOrganization(orgId).subscribe((response) => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/${orgId}`
      );
      expect(req.request.method).toBe('DELETE');
      req.flush(mockResponse);
    });
  });

  describe('addUserToOrganization', () => {
    it('should add user to organization', () => {
      const orgId = '663041cf7a14c7c000a3f999';
      const userData: AddUserToOrganizationRequest = {
        email: '<EMAIL>',
        role: 'orgmember',
        roleId: 'role123',
        roleName: 'Organization Member',
      };
      const mockResponse: MemberManagementResponse = {
        success: true,
        message: 'User successfully added to organization',
      };

      service.addUserToOrganization(orgId, userData).subscribe((response) => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/${orgId}/add-user`
      );
      expect(req.request.method).toBe('PATCH');
      expect(req.request.body).toEqual(userData);
      req.flush(mockResponse);
    });
  });

  describe('removeUserFromOrganization', () => {
    it('should remove user from organization', () => {
      const orgId = '663041cf7a14c7c000a3f999';
      const userData: RemoveUserFromOrganizationRequest = {
        email: '<EMAIL>',
      };
      const mockResponse: MemberManagementResponse = {
        success: true,
        message: 'User successfully removed from organization',
      };

      service
        .removeUserFromOrganization(orgId, userData)
        .subscribe((response) => {
          expect(response).toEqual(mockResponse);
        });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/${orgId}/remove-user`
      );
      expect(req.request.method).toBe('PATCH');
      expect(req.request.body).toEqual(userData);
      req.flush(mockResponse);
    });
  });

  describe('getOrganizationDetails', () => {
    it('should get organization details with members', () => {
      const orgId = '663041cf7a14c7c000a3f999';
      const mockResponse: OrganizationDetailsResponse = {
        success: true,
        data: {
          _id: '663041cf7a14c7c000a3f999',
          name: 'Acme Corporation',
          subdomain: 'acme-corp',
          branding: {
            logoUrl: 'https://acme.com/logo.png',
            primaryColor: '#336699',
          },
          createdAt: new Date('2025-06-02T17:35:17.117Z'),
          updatedAt: new Date('2025-06-02T17:35:17.117Z'),
          members: [
            {
              _id: 'member123',
              email: '<EMAIL>',
              name: 'John Member',
              status: 'active',
              role: {
                _id: 'role123',
                name: 'orgmember',
                displayName: 'Organization Member',
                hierarchy: { level: 1 },
              },
              assignedAt: new Date('2025-06-02T17:35:17.117Z'),
            },
          ],
        },
        metadata: {
          lookup_method: 'objectId',
          member_count: 1,
          retrieved_at: new Date('2025-06-02T17:35:17.117Z'),
        },
      };

      service.getOrganizationDetails(orgId).subscribe((response) => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/${orgId}/details`
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('createOrganizationByUser', () => {
    it('should create organization by authenticated user', () => {
      const mockResponse: CreateOrganizationResponse = {
        success: true,
        message: 'Organization created successfully (pending approval)',
        organization: { ...mockOrganization, status: 'pending' },
      };

      service
        .createOrganizationByUser(mockCreateRequest)
        .subscribe((response) => {
          expect(response).toEqual(mockResponse);
        });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/create`
      );
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockCreateRequest);
      req.flush(mockResponse);
    });
  });

  describe('approveOrganization', () => {
    it('should approve organization (God Super User only)', () => {
      const orgId = '663041cf7a14c7c000a3f999';
      const mockResponse: ApproveOrganizationResponse = {
        success: true,
        message: 'Organization approved successfully',
        organization: mockOrganization,
      };

      service.approveOrganization(orgId).subscribe((response) => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/${orgId}/approve`
      );
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual({});
      req.flush(mockResponse);
    });
  });

  describe('manageOrganizationMembers', () => {
    it('should add member to organization', () => {
      const orgId = '663041cf7a14c7c000a3f999';
      const memberData: MemberManagementRequest = {
        action: 'add',
        userEmail: '<EMAIL>',
        role: 'admin',
      };
      const mockResponse: MemberManagementResponse = {
        success: true,
        message: 'Member management operation successful',
      };

      service
        .manageOrganizationMembers(orgId, memberData)
        .subscribe((response) => {
          expect(response).toEqual(mockResponse);
        });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/${orgId}/members`
      );
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(memberData);
      req.flush(mockResponse);
    });

    it('should remove member from organization', () => {
      const orgId = '663041cf7a14c7c000a3f999';
      const memberData: MemberManagementRequest = {
        action: 'remove',
        userEmail: '<EMAIL>',
      };
      const mockResponse: MemberManagementResponse = {
        success: true,
        message: 'Member removed successfully',
      };

      service
        .manageOrganizationMembers(orgId, memberData)
        .subscribe((response) => {
          expect(response).toEqual(mockResponse);
        });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/${orgId}/members`
      );
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(memberData);
      req.flush(mockResponse);
    });

    it('should update member role in organization', () => {
      const orgId = '663041cf7a14c7c000a3f999';
      const memberData: MemberManagementRequest = {
        action: 'update',
        userEmail: '<EMAIL>',
        role: 'super_user',
      };
      const mockResponse: MemberManagementResponse = {
        success: true,
        message: 'Member role updated successfully',
      };

      service
        .manageOrganizationMembers(orgId, memberData)
        .subscribe((response) => {
          expect(response).toEqual(mockResponse);
        });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/organizations/${orgId}/members`
      );
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(memberData);
      req.flush(mockResponse);
    });
  });
});
