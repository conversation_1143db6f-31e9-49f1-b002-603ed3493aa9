import { Component, OnInit } from '@angular/core';
import { SubdomainService } from '../../../core/services/subdomain/subdomain.service';
import { AuthService } from '../../../core/services/auth/auth.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-subdomain-debug',
  template: `
    <div class="bg-gray-100 p-4 rounded-lg mb-4">
      <h3 class="text-lg font-semibold mb-3">Subdomain Debug Information</h3>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-white p-3 rounded">
          <h4 class="font-medium text-gray-700 mb-2">Environment</h4>
          <ul class="text-sm space-y-1">
            <li><strong>Production:</strong> {{ environment.production }}</li>
            <li>
              <strong>Enable Subdomains:</strong>
              {{ environment.enableSubdomains }}
            </li>
            <li>
              <strong>Subdomain Pattern:</strong>
              {{ environment.subdomainPattern }}
            </li>
            <li><strong>App Domain:</strong> {{ environment.appDomain }}</li>
          </ul>
        </div>

        <div class="bg-white p-3 rounded">
          <h4 class="font-medium text-gray-700 mb-2">Current State</h4>
          <ul class="text-sm space-y-1">
            <li><strong>Current Host:</strong> {{ currentHost }}</li>
            <li>
              <strong>Current Subdomain:</strong>
              {{ currentSubdomain || 'None' }}
            </li>
            <li>
              <strong>User Type:</strong> {{ userType || 'Not logged in' }}
            </li>
            <li>
              <strong>User Email:</strong> {{ userEmail || 'Not logged in' }}
            </li>
          </ul>
        </div>

        <div class="bg-white p-3 rounded">
          <h4 class="font-medium text-gray-700 mb-2">Default Organization</h4>
          <div *ngIf="defaultOrganization; else noDefaultOrg">
            <div class="text-sm space-y-1">
              <div><strong>Name:</strong> {{ defaultOrganization.name }}</div>
              <div>
                <strong>Subdomain:</strong> {{ defaultOrganization.subdomain }}
              </div>
              <div>
                <strong>Status:</strong> {{ defaultOrganization.status }}
              </div>
              <span
                class="inline-block mt-1 px-2 py-1 text-xs rounded bg-blue-100 text-blue-800"
              >
                Default Organization
              </span>
            </div>
          </div>
          <ng-template #noDefaultOrg>
            <p class="text-sm text-gray-500">No default organization</p>
          </ng-template>
        </div>

        <div class="bg-white p-3 rounded">
          <h4 class="font-medium text-gray-700 mb-2">All Organizations</h4>
          <div *ngIf="userOrganizations.length > 0; else noOrgs">
            <ul class="text-sm space-y-1">
              <li *ngFor="let org of userOrganizations">
                <strong>{{ org.name }}:</strong> {{ org.subdomain }}
                <span
                  class="ml-2 px-2 py-1 text-xs rounded"
                  [class]="getOrgStatusClass(org)"
                >
                  {{ getOrgStatusText(org) }}
                </span>
              </li>
            </ul>
          </div>
          <ng-template #noOrgs>
            <p class="text-sm text-gray-500">No organizations found</p>
          </ng-template>
        </div>

        <div class="bg-white p-3 rounded">
          <h4 class="font-medium text-gray-700 mb-2">Access Validation</h4>
          <ul class="text-sm space-y-1">
            <li>
              <strong>Has Subdomain Access:</strong>
              <span
                [class]="hasSubdomainAccess ? 'text-green-600' : 'text-red-600'"
              >
                {{ hasSubdomainAccess ? 'Yes' : 'No' }}
              </span>
            </li>
            <li>
              <strong>Available Subdomains:</strong>
              {{ availableSubdomains.join(', ') || 'None' }}
            </li>
          </ul>
        </div>
      </div>

      <div class="mt-4 bg-white p-3 rounded">
        <h4 class="font-medium text-gray-700 mb-2">Test Actions</h4>
        <div class="space-x-2">
          <button
            *ngFor="let subdomain of availableSubdomains"
            (click)="testRedirect(subdomain)"
            class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
          >
            Test {{ subdomain }}
          </button>
          <button
            (click)="testInvalidSubdomain()"
            class="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
          >
            Test Invalid Subdomain
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [],
})
export class SubdomainDebugComponent implements OnInit {
  environment = environment;
  currentHost = '';
  currentSubdomain: string | null = null;
  userType: string | null = null;
  userEmail: string | null = null;
  userOrganizations: any[] = [];
  defaultOrganization: any = null;
  hasSubdomainAccess = false;
  availableSubdomains: string[] = [];

  constructor(
    private subdomainService: SubdomainService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadDebugInfo();
  }

  private loadDebugInfo(): void {
    // Get current host and subdomain
    this.currentHost = window.location.host;
    this.currentSubdomain = this.subdomainService.getCurrentSubdomain();

    // Get user information
    const completeUser = this.authService.getCompleteUser();
    if (completeUser) {
      this.userType = completeUser.type;
      this.userEmail = completeUser.email;
      this.userOrganizations = completeUser.organizations || [];
      this.defaultOrganization = completeUser.defaultOrganization || null;
    }

    // Get available subdomains
    this.availableSubdomains = this.subdomainService.getUserSubdomains();

    // Check subdomain access
    if (this.currentSubdomain) {
      this.hasSubdomainAccess =
        this.subdomainService.validateUserSubdomainAccess(
          this.currentSubdomain
        );
    }
  }

  testRedirect(subdomain: string): void {
    console.log('Testing redirect to subdomain:', subdomain);
    this.subdomainService.redirectToSubdomain(subdomain, '/org-dashboard');
  }

  testInvalidSubdomain(): void {
    console.log('Testing redirect to invalid subdomain');
    const invalidSubdomain = 'nonexistent-org-' + Date.now();
    this.subdomainService.redirectToSubdomain(
      invalidSubdomain,
      '/org-dashboard'
    );
  }

  getOrgStatusClass(org: any): string {
    if (org.subdomain === this.currentSubdomain) {
      return 'bg-green-100 text-green-800';
    }
    if (this.defaultOrganization && org._id === this.defaultOrganization._id) {
      return 'bg-blue-100 text-blue-800';
    }
    return 'bg-gray-100 text-gray-600';
  }

  getOrgStatusText(org: any): string {
    if (org.subdomain === this.currentSubdomain) {
      return 'Current';
    }
    if (this.defaultOrganization && org._id === this.defaultOrganization._id) {
      return 'Default';
    }
    return 'Available';
  }
}
