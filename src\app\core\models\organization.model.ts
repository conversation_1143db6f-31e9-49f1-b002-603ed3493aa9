// Organization branding information
export interface OrganizationBranding {
  logoUrl?: string;
  primaryColor?: string;
}

// Basic organization information
export interface Organization {
  _id: string;
  name: string;
  subdomain: string;
  branding?: OrganizationBranding;
  status?: 'pending' | 'active' | 'inactive';
  createdAt?: Date;
  updatedAt?: Date;
}

// Team invitation for organization creation
export interface TeamInvitation {
  email: string;
  role: string;
}

// Organization creation request
export interface CreateOrganizationRequest {
  name: string;
  legalName?: string;
  subdomain: string;
  contactEmail: string;
  industryTag: string;
  organizationRoles: string[];
  branding?: OrganizationBranding;
  teamInvitations?: TeamInvitation[];
}

// Organization update request
export interface UpdateOrganizationRequest {
  name?: string;
  branding?: OrganizationBranding;
}

// Organization member role information
export interface OrganizationMemberRole {
  _id: string;
  name: string;
  displayName: string;
  hierarchy: {
    level: number;
  };
}

// Organization member information
export interface OrganizationMember {
  _id: string;
  email: string;
  name: string;
  status: string;
  role: OrganizationMemberRole;
  assignedAt: Date;
}

// Organization details with members
export interface OrganizationDetails {
  _id: string;
  name: string;
  subdomain: string;
  branding?: OrganizationBranding;
  createdAt: Date;
  updatedAt: Date;
  members: OrganizationMember[];
}

// Organization creator information
export interface OrganizationCreator {
  _id: string;
  name: string;
  email: string;
}

// Pending organization information
export interface PendingOrganization {
  _id: string;
  name: string;
  subdomain: string;
  branding?: OrganizationBranding;
  createdAt: Date;
  createdBy: OrganizationCreator;
  memberCount: number;
}

// Pagination information
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Paginated organizations response
export interface PaginatedOrganizationsResponse {
  page: number;
  limit: number;
  total: number;
  organizations: Organization[];
}

// Pending organizations response
export interface PendingOrganizationsResponse {
  success: boolean;
  data: {
    organizations: PendingOrganization[];
    pagination: PaginationInfo;
  };
  metadata: {
    retrieved_at: Date;
    duration_ms: number;
    privilege_level: string;
  };
}

// Organization details response
export interface OrganizationDetailsResponse {
  success: boolean;
  data: OrganizationDetails;
  metadata: {
    lookup_method: 'objectId' | 'subdomain';
    member_count: number;
    retrieved_at: Date;
  };
}

// Add user to organization request
export interface AddUserToOrganizationRequest {
  email: string;
  role: string;
  roleId?: string;
  roleName?: string;
}

// Remove user from organization request
export interface RemoveUserFromOrganizationRequest {
  email: string;
}

// Member management request
export interface MemberManagementRequest {
  action: 'add' | 'remove' | 'update';
  userEmail: string;
  role?: string;
}

// Organization creation response
export interface CreateOrganizationResponse {
  success: boolean;
  message: string;
  organization: Organization;
}

// Organization approval response
export interface ApproveOrganizationResponse {
  success: boolean;
  message: string;
  organization: Organization;
}

// Member management response
export interface MemberManagementResponse {
  success: boolean;
  message: string;
  member?: OrganizationMember;
}

// Generic organization operation response
export interface OrganizationOperationResponse {
  success: boolean;
  message: string;
  data?: any;
}

// Error response
export interface OrganizationErrorResponse {
  success: false;
  message: string;
  error: {
    code: string;
    details: string;
  };
}
