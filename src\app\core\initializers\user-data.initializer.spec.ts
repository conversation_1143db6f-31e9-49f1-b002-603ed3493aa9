import { TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';

import { initializeUserData, USER_DATA_INITIALIZER } from './user-data.initializer';
import { UserDataService } from '../services/user-data/user-data.service';
import { CompleteUser } from '../models/user.model';

describe('UserDataInitializer', () => {
  let userDataServiceSpy: jasmine.SpyObj<UserDataService>;

  const mockCompleteUser: CompleteUser = {
    _id: '123',
    name: 'Test User',
    email: '<EMAIL>',
    phone_number: '+1234567890',
    type: 'individual',
    status: 'active',
    profile: {
      fullName: 'Test User Full',
      jobTitle: 'Developer',
      companyName: 'Test Corp',
      bio: 'Test bio'
    },
    roles: [],
    systemPrivileges: [],
    organizations: [],
    privilegeSummary: {
      isGodSuperUser: false,
      hasSystemPrivileges: false,
      highestPrivilegeLevel: 0,
      organizationCount: 0
    }
  };

  beforeEach(() => {
    const userDataSpy = jasmine.createSpyObj('UserDataService', ['initializeUserData']);

    TestBed.configureTestingModule({
      providers: [
        { provide: UserDataService, useValue: userDataSpy }
      ]
    });

    userDataServiceSpy = TestBed.inject(UserDataService) as jasmine.SpyObj<UserDataService>;
  });

  describe('initializeUserData function', () => {
    it('should return a function that initializes user data', () => {
      const initFunction = initializeUserData(userDataServiceSpy);
      
      expect(typeof initFunction).toBe('function');
    });

    it('should call userDataService.initializeUserData and log success', (done) => {
      spyOn(console, 'log');
      userDataServiceSpy.initializeUserData.and.returnValue(of(mockCompleteUser));

      const initFunction = initializeUserData(userDataServiceSpy);
      
      initFunction().subscribe({
        next: (result) => {
          expect(result).toEqual(mockCompleteUser);
          expect(userDataServiceSpy.initializeUserData).toHaveBeenCalled();
          expect(console.log).toHaveBeenCalledWith('Initializing user data...');
          expect(console.log).toHaveBeenCalledWith('User data initialized successfully:', 'Test User');
          done();
        }
      });
    });

    it('should handle null user data and log appropriately', (done) => {
      spyOn(console, 'log');
      userDataServiceSpy.initializeUserData.and.returnValue(of(null));

      const initFunction = initializeUserData(userDataServiceSpy);
      
      initFunction().subscribe({
        next: (result) => {
          expect(result).toBeNull();
          expect(userDataServiceSpy.initializeUserData).toHaveBeenCalled();
          expect(console.log).toHaveBeenCalledWith('Initializing user data...');
          expect(console.log).toHaveBeenCalledWith('No authenticated user found');
          done();
        }
      });
    });

    it('should handle errors gracefully and return null', (done) => {
      spyOn(console, 'log');
      spyOn(console, 'warn');
      const error = new Error('Initialization failed');
      userDataServiceSpy.initializeUserData.and.returnValue(throwError(() => error));

      const initFunction = initializeUserData(userDataServiceSpy);
      
      initFunction().subscribe({
        next: (result) => {
          expect(result).toBeNull();
          expect(userDataServiceSpy.initializeUserData).toHaveBeenCalled();
          expect(console.log).toHaveBeenCalledWith('Initializing user data...');
          expect(console.warn).toHaveBeenCalledWith('Failed to initialize user data:', error);
          done();
        }
      });
    });
  });

  describe('USER_DATA_INITIALIZER provider', () => {
    it('should be configured correctly', () => {
      expect(USER_DATA_INITIALIZER.provide).toBeDefined();
      expect(USER_DATA_INITIALIZER.useFactory).toBe(initializeUserData);
      expect(USER_DATA_INITIALIZER.deps).toEqual([UserDataService]);
      expect(USER_DATA_INITIALIZER.multi).toBe(true);
    });

    it('should create initializer function when used as factory', () => {
      const factory = USER_DATA_INITIALIZER.useFactory;
      const initFunction = factory(userDataServiceSpy);
      
      expect(typeof initFunction).toBe('function');
    });
  });

  describe('Integration with Angular APP_INITIALIZER', () => {
    it('should work with Angular dependency injection', () => {
      // This test verifies that the provider configuration is correct
      // and can be used with Angular's APP_INITIALIZER token
      
      const provider = USER_DATA_INITIALIZER;
      
      // Verify provider structure matches APP_INITIALIZER requirements
      expect(provider.provide).toBeDefined();
      expect(provider.useFactory).toBeDefined();
      expect(provider.deps).toBeDefined();
      expect(provider.multi).toBe(true);
      
      // Verify the factory function can be called with dependencies
      const factoryResult = provider.useFactory(userDataServiceSpy);
      expect(typeof factoryResult).toBe('function');
    });

    it('should handle service initialization in Angular context', (done) => {
      spyOn(console, 'log');
      userDataServiceSpy.initializeUserData.and.returnValue(of(mockCompleteUser));

      // Simulate Angular calling the initializer
      const factory = USER_DATA_INITIALIZER.useFactory;
      const initFunction = factory(userDataServiceSpy);
      
      initFunction().subscribe({
        next: (result) => {
          expect(result).toEqual(mockCompleteUser);
          expect(console.log).toHaveBeenCalledWith('Initializing user data...');
          done();
        }
      });
    });
  });

  describe('Error scenarios', () => {
    it('should not throw errors when userDataService throws', (done) => {
      spyOn(console, 'warn');
      userDataServiceSpy.initializeUserData.and.returnValue(
        throwError(() => new Error('Service unavailable'))
      );

      const initFunction = initializeUserData(userDataServiceSpy);
      
      initFunction().subscribe({
        next: (result) => {
          expect(result).toBeNull();
          expect(console.warn).toHaveBeenCalled();
          done();
        },
        error: () => {
          fail('Should not emit error, should handle gracefully');
        }
      });
    });

    it('should handle network errors gracefully', (done) => {
      spyOn(console, 'warn');
      const networkError = new Error('Network error');
      userDataServiceSpy.initializeUserData.and.returnValue(throwError(() => networkError));

      const initFunction = initializeUserData(userDataServiceSpy);
      
      initFunction().subscribe({
        next: (result) => {
          expect(result).toBeNull();
          expect(console.warn).toHaveBeenCalledWith('Failed to initialize user data:', networkError);
          done();
        }
      });
    });

    it('should handle authentication errors gracefully', (done) => {
      spyOn(console, 'warn');
      const authError = new Error('User not authenticated');
      userDataServiceSpy.initializeUserData.and.returnValue(throwError(() => authError));

      const initFunction = initializeUserData(userDataServiceSpy);
      
      initFunction().subscribe({
        next: (result) => {
          expect(result).toBeNull();
          expect(console.warn).toHaveBeenCalledWith('Failed to initialize user data:', authError);
          done();
        }
      });
    });
  });

  describe('Logging behavior', () => {
    beforeEach(() => {
      spyOn(console, 'log');
      spyOn(console, 'warn');
    });

    it('should log initialization start', () => {
      userDataServiceSpy.initializeUserData.and.returnValue(of(null));
      
      const initFunction = initializeUserData(userDataServiceSpy);
      initFunction().subscribe();
      
      expect(console.log).toHaveBeenCalledWith('Initializing user data...');
    });

    it('should log successful initialization with user name', () => {
      userDataServiceSpy.initializeUserData.and.returnValue(of(mockCompleteUser));
      
      const initFunction = initializeUserData(userDataServiceSpy);
      initFunction().subscribe();
      
      expect(console.log).toHaveBeenCalledWith('User data initialized successfully:', 'Test User');
    });

    it('should log when no user is found', () => {
      userDataServiceSpy.initializeUserData.and.returnValue(of(null));
      
      const initFunction = initializeUserData(userDataServiceSpy);
      initFunction().subscribe();
      
      expect(console.log).toHaveBeenCalledWith('No authenticated user found');
    });

    it('should warn when initialization fails', () => {
      const error = new Error('Test error');
      userDataServiceSpy.initializeUserData.and.returnValue(throwError(() => error));
      
      const initFunction = initializeUserData(userDataServiceSpy);
      initFunction().subscribe();
      
      expect(console.warn).toHaveBeenCalledWith('Failed to initialize user data:', error);
    });
  });
});
