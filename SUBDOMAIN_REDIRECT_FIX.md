# Subdomain Redirection Fix

## Problem Description

After login on production, users were not being properly redirected to their organization's subdomain dashboard. The main issues were:

1. **Authentication Token Loss**: When users logged in on the main domain (`digimeet.live`) and were redirected to their organization subdomain (`orgname.digimeet.live`), the authentication tokens stored in localStorage on the main domain were not accessible to the subdomain due to browser security policies.

2. **Missing Subdomain Validation**: Any subdomain could display the dashboard without proper validation of user access.

3. **Inconsistent Redirection**: Subdomain redirection was not working consistently across different authentication flows.

## Root Cause

The main issue was that localStorage is domain-specific. When a user logs in on `digimeet.live`, their tokens are stored in localStorage for that domain. When they're redirected to `orgname.digimeet.live`, the subdomain cannot access the tokens from the parent domain, causing the user to appear as unauthenticated.

## Solution Overview

The fix implements a **token transfer mechanism** that securely passes authentication tokens from the main domain to the subdomain during redirection.

### Key Components

1. **Enhanced Login Component** (`src/app/features/auth/login/login.component.ts`)

   - Modified to transfer tokens via URL parameters when redirecting to subdomains
   - Uses `redirectToSubdomainWithTokens()` method

2. **Subdomain Login Component** (`src/app/features/auth/subdomain-login/subdomain-login.component.ts`)

   - Receives tokens from URL parameters
   - Stores tokens in subdomain's localStorage
   - Redirects to the intended dashboard

3. **Enhanced Subdomain Service** (`src/app/core/services/subdomain/subdomain.service.ts`)

   - Centralized subdomain operations
   - Handles token transfer during redirection

4. **Subdomain Validation Guard** (`src/app/core/guards/subdomain-validation.guard.ts`)
   - Validates user access to specific organization subdomains
   - Prevents unauthorized access

## How Token Transfer Works

### Step 1: Login on Main Domain

User logs in on `digimeet.live` → tokens stored in main domain localStorage

### Step 2: Subdomain Redirection with Tokens

Instead of direct redirect to `orgname.digimeet.live/org-dashboard`, the system:

1. Extracts current access and refresh tokens
2. Redirects to `orgname.digimeet.live/auth/subdomain-login?token=...&refresh=...&redirect=org-dashboard`

### Step 3: Token Reception on Subdomain

The `SubdomainLoginComponent`:

1. Extracts tokens from URL parameters
2. Stores them in subdomain's localStorage
3. Redirects to the intended dashboard (`/org-dashboard`)

### Step 4: Authenticated Access

User now has valid tokens on the subdomain and can access the organization dashboard

## Security Considerations

✅ **Secure Token Transfer**: Tokens are passed via HTTPS URLs and immediately stored in localStorage
✅ **URL Cleanup**: Tokens are removed from URL after processing
✅ **Timeout Protection**: 10-second timeout prevents infinite loading
✅ **Error Handling**: Comprehensive error handling with fallback options
✅ **Access Validation**: SubdomainValidationGuard ensures users can only access authorized subdomains

## Code Changes

### 1. Login Component Enhancement

```typescript
private redirectToSubdomainWithTokens(subdomainHost: string, subdomain: string) {
  const accessToken = this.authService.getAccessToken();
  const refreshToken = this.authService.getRefreshToken();

  const subdomainUrl = `${protocol}//${subdomainHost}/auth/subdomain-login?token=${encodeURIComponent(accessToken)}&refresh=${encodeURIComponent(refreshToken)}&redirect=org-dashboard`;

  window.location.href = subdomainUrl;
}
```

### 2. Subdomain Service Enhancement

```typescript
redirectToSubdomain(subdomain: string, path: string = '/org-dashboard'): void {
  const accessToken = this.authService.getAccessToken();
  const refreshToken = this.authService.getRefreshToken();

  const subdomainUrl = `${protocol}//${subdomainHost}/auth/subdomain-login?token=${encodeURIComponent(accessToken)}&refresh=${encodeURIComponent(refreshToken)}&redirect=${redirectPath}`;

  window.location.href = subdomainUrl;
}
```

### 3. Subdomain Login Component

```typescript
private authenticateWithTokens(token: string, refreshToken: string, redirectPath: string): void {
  localStorage.setItem('idToken', token);
  localStorage.setItem('refreshToken', refreshToken);

  setTimeout(() => {
    this.router.navigate([`/${redirectPath}`]);
  }, 500);
}
```

## Testing

### Test Scenarios

1. ✅ Login on main domain → Redirect to organization subdomain
2. ✅ Token transfer and storage on subdomain
3. ✅ Successful authentication on subdomain
4. ✅ Access validation for correct organization
5. ✅ Error handling for invalid tokens
6. ✅ Fallback for missing tokens

### Production URLs

- Main domain: `https://digimeet.live`
- Organization subdomain: `https://orgname.digimeet.live`
- Token transfer URL: `https://orgname.digimeet.live/auth/subdomain-login?token=...&refresh=...&redirect=org-dashboard`

## Critical Fix Applied

### Environment File Replacement Issue

**CRITICAL**: The Angular build configuration was missing the environment file replacement for production builds. This meant that even in production, the app was using the development environment settings (`enableSubdomains: false`).

**Fixed in `angular.json`:**

```json
"production": {
  "fileReplacements": [
    {
      "replace": "src/environments/environment.ts",
      "with": "src/environments/environment.prod.ts"
    }
  ]
}
```

This fix ensures that production builds actually use the production environment file with `enableSubdomains: true`.

## Deployment Notes

1. **REBUILD REQUIRED**: After this fix, the application must be rebuilt for production
2. **Environment Verification**: Added extensive logging to verify environment settings
3. **No Breaking Changes**: Existing functionality preserved
4. **Backward Compatible**: Works in both development and production
5. **Environment Aware**: Now correctly detects production vs development
6. **Graceful Degradation**: Falls back to regular routing if token transfer fails

## Monitoring

The implementation includes extensive logging:

- Token extraction and validation
- Subdomain redirection attempts
- Token storage on subdomain
- Authentication success/failure
- Error conditions

Search for these log prefixes in browser console:

- `LOGIN: Redirecting to subdomain with tokens`
- `SubdomainLoginComponent: Setting tokens in localStorage`
- `SubdomainValidationGuard: Validating subdomain access`

## Environment Configuration

### Production (`environment.prod.ts`)

```typescript
export const environment = {
  production: true,
  enableSubdomains: true,
  subdomainPattern: "{subdomain}.digimeet.live",
  appDomain: "digimeet.live",
};
```

This fix ensures that users can successfully log in on the main domain and be properly redirected to their organization's subdomain with full authentication state preserved.

## Organization Switcher Feature

### Multi-Organization Support

For users who belong to multiple organizations, the system now includes a sophisticated organization switcher component that allows seamless switching between different organization subdomains.

### Features

✅ **Visual Organization Switcher**: Dropdown component in the organization layout header
✅ **Default Organization Priority**: Highlights and prioritizes user's default organization
✅ **Current Organization Indicator**: Shows which organization is currently active
✅ **Organization Branding**: Displays organization logos and brand colors
✅ **Seamless Subdomain Switching**: Automatically redirects to selected organization's subdomain
✅ **Responsive Design**: Works on both desktop and mobile devices
✅ **Smart Visibility**: Only shows when user has access to multiple organizations

### How It Works

1. **Detection**: Component detects if user has multiple organizations
2. **Display**: Shows current organization with dropdown arrow
3. **Selection**: User clicks to see all available organizations
4. **Switching**: Clicking another organization redirects to its subdomain
5. **Validation**: All switches are validated against user's actual permissions

### Organization Switcher UI

```typescript
// Shows in organization layout header
<app-organization-switcher></app-organization-switcher>
```

The switcher displays:

- **Current Organization**: Name and subdomain
- **Organization List**: All accessible organizations with:
  - Organization logo or initials
  - Organization name and subdomain
  - "Default" badge for default organization
  - "Current" badge for active organization
  - Organization status

### Example User Experience

For a user with organizations: `aassa`, `iwillqqq`, `aaa`, `aasa`:

1. **Login**: Redirects to default organization (`aassa.digimeet.live`)
2. **Switcher**: Shows "Aacme Corporation" with dropdown
3. **Options**: Lists all 4 organizations with visual indicators
4. **Switch**: Click "testme corpq" → redirects to `iwillqqq.digimeet.live`
5. **Validation**: System validates access before redirect

### Security

- **Permission-Based**: Only shows organizations user actually has access to
- **Real-Time Validation**: Uses live user data from `/api/v1/users/me`
- **Secure Redirects**: All redirects go through subdomain validation
- **Token Preservation**: Authentication tokens maintained across switches
