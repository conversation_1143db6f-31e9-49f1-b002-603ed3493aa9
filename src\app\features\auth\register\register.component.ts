import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from '../../../core/services/auth/auth.service';
import { UserDataService } from '../../../core/services/user-data/user-data.service';
import { AlertType } from '../../../shared/components/alert/alert.component';

type AuthMethod = 'password' | 'otp';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css'],
})
export class RegisterComponent implements OnInit {
  authForm!: FormGroup;
  loading = false;
  submitted = false;
  returnUrl: string = '/onboarding';
  error: string = '';
  showAlert = false;
  alertType: AlertType = 'error';
  alertMessage: string = '';

  // Authentication method and mode
  authMethod: AuthMethod = 'password';
  isLoginMode = false; // false = register mode, true = login mode

  // OTP related
  otpSent = false;
  otpRequestId: string = '';

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private userDataService: UserDataService
  ) {
    // Redirect to dashboard if already logged in
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
    }
  }

  ngOnInit() {
    // Get return URL from route parameters or default to onboarding
    this.returnUrl =
      this.route.snapshot.queryParams['returnUrl'] || '/onboarding';

    // Check if we should start in login mode
    const mode = this.route.snapshot.queryParams['mode'];
    if (mode === 'login') {
      this.isLoginMode = true;
      this.returnUrl = '/dashboard';
    }

    this.initializeForm();
  }

  private initializeForm() {
    this.authForm = this.formBuilder.group({
      identifier: ['', [Validators.required]], // Can be email or phone
      password: [''],
      name: [''],
      otp: [''],
    });

    this.updateValidators();
  }

  private updateValidators() {
    // Clear all validators first
    Object.keys(this.authForm.controls).forEach((key) => {
      this.authForm.get(key)?.clearValidators();
    });

    // Set validators based on auth method and mode
    this.authForm.get('identifier')?.setValidators([Validators.required]);

    if (this.authMethod === 'password') {
      this.authForm
        .get('password')
        ?.setValidators([Validators.required, Validators.minLength(6)]);
      if (!this.isLoginMode) {
        this.authForm.get('name')?.setValidators([Validators.required]);
      }
    } else if (this.authMethod === 'otp') {
      if (!this.isLoginMode) {
        this.authForm.get('name')?.setValidators([Validators.required]);
      }
      if (this.otpSent) {
        this.authForm
          .get('otp')
          ?.setValidators([Validators.required, Validators.minLength(6)]);
      }
    }

    // Update form validation
    Object.keys(this.authForm.controls).forEach((key) => {
      this.authForm.get(key)?.updateValueAndValidity();
    });
  }

  toggleMode() {
    this.isLoginMode = !this.isLoginMode;
    this.returnUrl = this.isLoginMode ? '/dashboard' : '/onboarding';
    this.showAlert = false;
    this.otpSent = false;
    this.updateValidators();
  }

  setAuthMethod(method: AuthMethod) {
    this.authMethod = method;
    this.showAlert = false;
    this.otpSent = false;
    this.authForm.reset();
    this.updateValidators();
  }

  getAuthMethodButtonClass(method: AuthMethod): string {
    const baseClass =
      'px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200';
    const activeClass = 'bg-indigo-600 text-white';
    const inactiveClass = 'bg-gray-200 text-gray-700 hover:bg-gray-300';

    return `${baseClass} ${
      this.authMethod === method ? activeClass : inactiveClass
    }`;
  }

  getSubmitButtonText(): string {
    if (this.authMethod === 'otp' && !this.otpSent) {
      return this.isLoginMode ? 'Send Login OTP' : 'Send Registration OTP';
    }
    if (this.authMethod === 'otp' && this.otpSent) {
      return this.isLoginMode ? 'Verify & Login' : 'Verify & Register';
    }
    return this.isLoginMode ? 'Sign In' : 'Create Account';
  }

  onSubmit() {
    this.submitted = true;
    this.showAlert = false;

    // Stop here if form is invalid
    if (this.authForm.invalid) {
      return;
    }

    this.loading = true;

    if (this.authMethod === 'otp') {
      // Phone OTP temporarily disabled due to API mismatch
      this.handleError({
        message:
          'OTP authentication is temporarily unavailable. Please use password authentication.',
      });
      this.loading = false;
      return;
    } else {
      this.handleStandardAuth();
    }
  }

  // Phone OTP functionality temporarily disabled due to API mismatch

  private handleStandardAuth() {
    const identifier = this.authForm.get('identifier')?.value;

    if (this.isLoginMode) {
      // Login
      this.authService
        .login({
          identifier: identifier,
          password: this.authForm.get('password')?.value,
        })
        .subscribe({
          next: () => {
            this.handleSuccessfulAuth();
          },
          error: (error) => {
            this.handleError(error);
          },
        });
    } else {
      // Registration - determine if identifier is email or phone
      const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(identifier);

      const registrationData = {
        email: isEmail ? identifier : '',
        phone_number: isEmail ? '' : identifier,
        name: this.authForm.get('name')?.value,
        password: this.authForm.get('password')?.value,
      };

      this.authService.register(registrationData).subscribe({
        next: () => {
          this.handleSuccessfulAuth();
        },
        error: (error) => {
          this.handleError(error);
        },
      });
    }
  }

  private handleSuccessfulAuth() {
    this.loading = false;

    if (this.isLoginMode) {
      // For login, implement post-auth routing logic
      this.handlePostAuthRouting();
    } else {
      // For registration, redirect to onboarding
      this.router.navigate(['/onboarding']);
    }
  }

  private handlePostAuthRouting() {
    // For new registrations, always go to onboarding first
    this.router.navigate(['/onboarding']);
  }

  // resendOtp method removed - Phone OTP temporarily disabled

  private handleError(error: any) {
    this.loading = false;
    this.alertMessage = error.message || 'An error occurred. Please try again.';
    this.alertType = 'error';
    this.showAlert = true;
  }

  // showSuccessAlert method removed - not needed without OTP functionality
}
