/* Individual profile setup specific styles */
.tag-input {
  transition: all 0.2s ease;
}

.tag-input:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.tag-button {
  transition: all 0.2s ease;
}

.tag-button:hover:not(:disabled) {
  background-color: #f3f4f6;
  border-color: #6b7280;
}

.selected-tag {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.form-section {
  margin-bottom: 2rem;
}

.character-counter {
  font-size: 0.75rem;
  color: #6b7280;
}

.character-counter.warning {
  color: #f59e0b;
}

.character-counter.danger {
  color: #ef4444;
}
