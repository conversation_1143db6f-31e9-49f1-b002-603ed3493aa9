import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { OrganizationDataService } from '../../../core/services/organization-data/organization-data.service';
import {
  Organization,
  CreateOrganizationRequest,
  UpdateOrganizationRequest,
  PaginatedOrganizationsResponse,
  AddUserToOrganizationRequest,
  RemoveUserFromOrganizationRequest,
} from '../../../core/models/organization.model';

@Component({
  selector: 'app-organization-management',
  templateUrl: './organization-management.component.html',
  styleUrls: ['./organization-management.component.css'],
})
export class OrganizationManagementComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  currentOrganization: Organization | null = null;
  userOrganizations: Organization[] = [];
  allOrganizations: Organization[] = [];
  isLoading = false;
  isAdmin = false;
  isGodSuperUser = false;
  canManage = false;

  // Form data
  createOrgForm: CreateOrganizationRequest = {
    name: '',
    subdomain: '',
    contactEmail: '',
    industryTag: '',
    organizationRoles: [],
    branding: {
      logoUrl: '',
      primaryColor: '#336699',
    },
  };

  updateOrgForm: UpdateOrganizationRequest = {
    name: '',
    branding: {
      logoUrl: '',
      primaryColor: '',
    },
  };

  addUserForm: AddUserToOrganizationRequest = {
    email: '',
    role: 'orgmember',
  };

  constructor(private organizationDataService: OrganizationDataService) {}

  ngOnInit(): void {
    // Subscribe to current organization changes
    this.organizationDataService.currentOrganization$
      .pipe(takeUntil(this.destroy$))
      .subscribe((org) => {
        this.currentOrganization = org;
        if (org) {
          this.updateOrgForm = {
            name: org.name,
            branding: {
              logoUrl: org.branding?.logoUrl || '',
              primaryColor: org.branding?.primaryColor || '#336699',
            },
          };
        }
      });

    // Subscribe to loading state
    this.organizationDataService.isLoading$
      .pipe(takeUntil(this.destroy$))
      .subscribe((loading) => {
        this.isLoading = loading;
      });

    // Check user permissions
    this.organizationDataService
      .isOrganizationAdmin()
      .pipe(takeUntil(this.destroy$))
      .subscribe((isAdmin) => {
        this.isAdmin = isAdmin;
      });

    this.organizationDataService
      .isGodSuperUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe((isGod) => {
        this.isGodSuperUser = isGod;
      });

    this.organizationDataService
      .canManageOrganization()
      .pipe(takeUntil(this.destroy$))
      .subscribe((canManage) => {
        this.canManage = canManage;
      });

    // Load user organizations
    this.loadUserOrganizations();

    // Initialize organization data
    this.organizationDataService.initializeOrganizationData().subscribe();

    // Load all organizations if user is God Super User
    if (this.isGodSuperUser) {
      this.loadAllOrganizations();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadUserOrganizations(): void {
    this.organizationDataService
      .getUserOrganizations()
      .pipe(takeUntil(this.destroy$))
      .subscribe((organizations) => {
        this.userOrganizations = organizations;
      });
  }

  loadAllOrganizations(): void {
    this.organizationDataService.organizationApi
      .getAllOrganizations(1, 50)
      .subscribe({
        next: (response: PaginatedOrganizationsResponse) => {
          this.allOrganizations = response.organizations;
        },
        error: (error) => {
          console.error('Failed to load all organizations:', error);
        },
      });
  }

  createOrganization(): void {
    if (
      !this.createOrgForm.name ||
      !this.createOrgForm.subdomain ||
      !this.createOrgForm.contactEmail ||
      !this.createOrgForm.industryTag ||
      !this.createOrgForm.organizationRoles ||
      this.createOrgForm.organizationRoles.length === 0
    ) {
      alert(
        'Please fill in all required fields (name, subdomain, contact email, industry, and at least one organization role)'
      );
      return;
    }

    this.organizationDataService
      .createOrganizationByUser(this.createOrgForm)
      .subscribe({
        next: (response) => {
          console.log('Organization created:', response);
          this.resetCreateForm();
          this.loadUserOrganizations();
        },
        error: (error) => {
          console.error('Failed to create organization:', error);
          alert('Failed to create organization. Please try again.');
        },
      });
  }

  updateOrganization(): void {
    if (!this.currentOrganization) {
      alert('No organization selected');
      return;
    }

    this.organizationDataService
      .updateOrganization(this.currentOrganization._id, this.updateOrgForm)
      .subscribe({
        next: (organization) => {
          console.log('Organization updated:', organization);
          alert('Organization updated successfully');
        },
        error: (error) => {
          console.error('Failed to update organization:', error);
          alert('Failed to update organization. Please try again.');
        },
      });
  }

  deleteOrganization(): void {
    if (!this.currentOrganization) {
      alert('No organization selected');
      return;
    }

    if (
      confirm(
        `Are you sure you want to delete "${this.currentOrganization.name}"? This action cannot be undone.`
      )
    ) {
      this.organizationDataService
        .deleteOrganization(this.currentOrganization._id)
        .subscribe({
          next: (response) => {
            console.log('Organization deleted:', response);
            alert('Organization deleted successfully');
            this.loadUserOrganizations();
          },
          error: (error) => {
            console.error('Failed to delete organization:', error);
            alert('Failed to delete organization. Please try again.');
          },
        });
    }
  }

  selectOrganization(organization: Organization): void {
    this.organizationDataService.setCurrentOrganization(organization);
  }

  loadOrganizationDetails(orgId: string): void {
    this.organizationDataService.loadOrganizationDetails(orgId).subscribe({
      next: (response) => {
        console.log('Organization details loaded:', response);
      },
      error: (error) => {
        console.error('Failed to load organization details:', error);
      },
    });
  }

  addUserToOrganization(): void {
    if (!this.currentOrganization || !this.addUserForm.email) {
      alert('Please select an organization and enter user email');
      return;
    }

    this.organizationDataService.organizationApi
      .addUserToOrganization(this.currentOrganization._id, this.addUserForm)
      .subscribe({
        next: (response) => {
          console.log('User added to organization:', response);
          alert('User added successfully');
          this.resetAddUserForm();
          this.refreshCurrentOrganization();
        },
        error: (error) => {
          console.error('Failed to add user:', error);
          alert('Failed to add user. Please try again.');
        },
      });
  }

  removeUserFromOrganization(userEmail: string): void {
    if (!this.currentOrganization) {
      alert('No organization selected');
      return;
    }

    const removeData: RemoveUserFromOrganizationRequest = { email: userEmail };

    this.organizationDataService.organizationApi
      .removeUserFromOrganization(this.currentOrganization._id, removeData)
      .subscribe({
        next: (response) => {
          console.log('User removed from organization:', response);
          alert('User removed successfully');
          this.refreshCurrentOrganization();
        },
        error: (error) => {
          console.error('Failed to remove user:', error);
          alert('Failed to remove user. Please try again.');
        },
      });
  }

  refreshCurrentOrganization(): void {
    this.organizationDataService.refreshCurrentOrganization().subscribe({
      next: (organization) => {
        console.log('Organization refreshed:', organization);
      },
      error: (error) => {
        console.error('Failed to refresh organization:', error);
      },
    });
  }

  resetCreateForm(): void {
    this.createOrgForm = {
      name: '',
      subdomain: '',
      contactEmail: '',
      industryTag: '',
      organizationRoles: [],
      branding: {
        logoUrl: '',
        primaryColor: '#336699',
      },
    };
  }

  resetAddUserForm(): void {
    this.addUserForm = {
      email: '',
      role: 'orgmember',
    };
  }

  // Helper methods for template
  get organizationName(): string {
    return this.currentOrganization?.name || 'No Organization Selected';
  }

  get organizationSubdomain(): string {
    return this.currentOrganization?.subdomain || '';
  }

  get organizationStatus(): string {
    return this.currentOrganization?.status || 'unknown';
  }
}
