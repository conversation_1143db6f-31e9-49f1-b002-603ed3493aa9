import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AuthService } from '../../core/services/auth/auth.service';
import { OrganizationDataService } from '../../core/services/organization-data/organization-data.service';
import { UserDataService } from '../../core/services/user-data/user-data.service';
import { SubdomainService } from '../../core/services/subdomain/subdomain.service';
import {
  Organization as UserOrganization,
  CompleteUser,
} from '../../core/models/user.model';
import { Organization } from '../../core/models/organization.model';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-organization-layout',
  templateUrl: './organization-layout.component.html',
  styleUrls: ['./organization-layout.component.css'],
})
export class OrganizationLayoutComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  currentUser: CompleteUser | null = null;
  currentOrganization: UserOrganization | null = null;
  isLoading = false;
  isSidebarOpen = false;

  constructor(
    private authService: AuthService,
    private organizationDataService: OrganizationDataService,
    private userDataService: UserDataService,
    private router: Router,
    private subdomainService: SubdomainService
  ) {}

  ngOnInit(): void {
    this.loadUserAndOrganizationData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadUserAndOrganizationData(): void {
    // Load complete user data
    this.userDataService.completeUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe((user) => {
        this.currentUser = user;
        if (user && user.organizations && user.organizations.length > 0) {
          // Prioritize default organization, then fall back to first organization
          let targetOrg = user.defaultOrganization;

          if (!targetOrg && user.organizations.length > 0) {
            targetOrg = user.organizations[0];
          }

          if (targetOrg) {
            this.currentOrganization = targetOrg;
            // Convert to organization model format for the service
            const orgForService: Organization = {
              _id: targetOrg._id,
              name: targetOrg.name,
              subdomain: targetOrg.subdomain,
              branding: targetOrg.branding,
              status: targetOrg.status as
                | 'pending'
                | 'active'
                | 'inactive'
                | undefined,
            };
            this.organizationDataService.setCurrentOrganization(orgForService);
          }
        }
      });

    // Subscribe to loading states
    this.userDataService.isLoading$
      .pipe(takeUntil(this.destroy$))
      .subscribe((loading) => {
        this.isLoading = loading;
      });

    // Load user data if not already loaded, but avoid API calls on subdomains
    if (!this.currentUser) {
      const currentSubdomain = this.subdomainService.getCurrentSubdomain();
      if (currentSubdomain) {
        console.log(
          'OrganizationLayoutComponent: On subdomain, avoiding API call to prevent CORS issues'
        );
        console.log(
          'OrganizationLayoutComponent: User data should be available from subdomain login process'
        );
      } else {
        console.log(
          'OrganizationLayoutComponent: On main domain, loading user data'
        );
        this.userDataService.loadCompleteUserData().subscribe();
      }
    }
  }

  toggleSidebar(): void {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  logout(): void {
    console.log('OrganizationLayout: Logging out user');

    // If on subdomain, use cross-domain logout to clear tokens from both domains
    if (this.subdomainService.areSubdomainsEnabled()) {
      const currentSubdomain = this.subdomainService.getCurrentSubdomain();
      if (currentSubdomain) {
        console.log(
          'OrganizationLayout: On subdomain, initiating cross-domain logout'
        );
        // Use the new cross-domain logout method
        this.authService.logoutFromSubdomain();
        this.authService.logoutFromSubdomain();
        return;
      }
    }

    // Fallback for main domain logout
    console.log('OrganizationLayout: On main domain, using regular logout');
    this.authService.logout();
    this.router.navigate(['/auth/login']);
  }

  get organizationName(): string {
    return this.currentOrganization?.name || 'Organization';
  }

  get userName(): string {
    return this.currentUser?.name || 'User';
  }

  get userEmail(): string {
    return this.currentUser?.email || '';
  }

  getSubdomainUrl(): string {
    if (!this.currentOrganization?.subdomain) {
      return '';
    }

    return environment.subdomainPattern.replace(
      '{subdomain}',
      this.currentOrganization.subdomain
    );
  }

  getOrganizationLogoUrl(): string {
    // Return organization logo URL or fallback to existing logo
    return this.currentOrganization?.branding?.logoUrl || '/assets/logo.png';
  }

  onImageError(event: any): void {
    // Fallback to existing logo if organization logo fails to load
    console.log('Organization logo failed to load, using fallback');
    event.target.src = '/assets/logo.png';
  }
}
