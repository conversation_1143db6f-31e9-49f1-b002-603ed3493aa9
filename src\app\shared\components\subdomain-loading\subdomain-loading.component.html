<div class="subdomain-loading-container">
  <div class="loading-card">
    <!-- Organization Logo/Icon -->
    <div class="logo-container">
      <div class="logo-placeholder">
        <svg class="w-12 h-12 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
          </path>
        </svg>
      </div>
    </div>

    <!-- Loading Message -->
    <div class="message-container">
      <h2 class="loading-title">{{ loadingMessage }}</h2>
      <p class="loading-subtitle" *ngIf="!showRetryButton">
        Please wait while we set up your workspace...
      </p>
    </div>

    <!-- Progress Bar -->
    <div class="progress-container" *ngIf="!showRetryButton">
      <div class="progress-bar">
        <div class="progress-fill" [style.width.%]="progress"></div>
      </div>
      <div class="progress-text">{{ progress.toFixed(0) }}%</div>
    </div>

    <!-- Loading Animation -->
    <div class="loading-animation" *ngIf="!showRetryButton">
      <div class="spinner"></div>
    </div>

    <!-- Retry Options -->
    <div class="retry-container" *ngIf="showRetryButton">
      <p class="retry-message">
        We're having trouble loading your data. This sometimes happens due to network timing.
      </p>
      
      <div class="retry-buttons">
        <button class="btn btn-primary" (click)="retryLoading()">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
            </path>
          </svg>
          Try Again
        </button>
        
        <button class="btn btn-secondary" (click)="forceRefresh()">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
            </path>
          </svg>
          Refresh Page
        </button>
        
        <button class="btn btn-outline" (click)="goToDashboard()">
          Continue Anyway
        </button>
      </div>
    </div>

    <!-- Debug Info (only in development) -->
    <div class="debug-info" *ngIf="!showRetryButton">
      <small class="text-gray-500">
        Initializing secure connection...
      </small>
    </div>
  </div>
</div>
