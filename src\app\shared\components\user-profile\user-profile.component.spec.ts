import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { UserProfileComponent } from './user-profile.component';
import { UserDataService } from '../../../core/services/user-data/user-data.service';
import { CompleteUser, UserProfileUpdateRequest } from '../../../core/models/user.model';

describe('UserProfileComponent', () => {
  let component: UserProfileComponent;
  let fixture: ComponentFixture<UserProfileComponent>;
  let userDataServiceSpy: jasmine.SpyObj<UserDataService>;

  const mockCompleteUser: CompleteUser = {
    _id: '123',
    name: 'Test User',
    email: '<EMAIL>',
    phone_number: '+1234567890',
    type: 'individual',
    status: 'active',
    profile: {
      fullName: 'Test User Full',
      jobTitle: 'Software Developer',
      companyName: 'Test Corp',
      bio: 'Experienced software developer with 5+ years in web development.',
      industryTags: ['Technology', 'Software Development'],
      networkingGoal: 'Looking to connect with other developers',
      timezone: 'UTC',
      language: 'English'
    },
    roles: [
      { org: 'org1', role: 'USER', assignedAt: new Date() }
    ],
    systemPrivileges: [],
    organizations: [
      {
        _id: 'org1',
        name: 'Test Organization',
        subdomain: 'test-org',
        branding: {
          logoUrl: 'https://example.com/logo.png',
          primaryColor: '#007bff'
        }
      }
    ],
    privilegeSummary: {
      isGodSuperUser: false,
      hasSystemPrivileges: false,
      highestPrivilegeLevel: 1,
      organizationCount: 1
    }
  };

  const mockPermissions = {
    isAdmin: false,
    isGodSuperUser: false,
    hasSystemPrivileges: false,
    organizationCount: 1,
    roles: ['USER']
  };

  beforeEach(async () => {
    const userDataSpy = jasmine.createSpyObj('UserDataService', [
      'refreshUserData',
      'updateProfile',
      'deleteAccount',
      'getUserPermissions'
    ], {
      completeUser$: of(mockCompleteUser),
      isLoading$: of(false)
    });

    await TestBed.configureTestingModule({
      declarations: [UserProfileComponent],
      providers: [
        { provide: UserDataService, useValue: userDataSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(UserProfileComponent);
    component = fixture.componentInstance;
    userDataServiceSpy = TestBed.inject(UserDataService) as jasmine.SpyObj<UserDataService>;

    userDataServiceSpy.getUserPermissions.and.returnValue(of(mockPermissions));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with user data on ngOnInit', () => {
    fixture.detectChanges();

    expect(component.completeUser).toEqual(mockCompleteUser);
    expect(component.userPermissions).toEqual(mockPermissions);
    expect(component.isAdmin).toBe(false);
  });

  it('should display user basic information', () => {
    fixture.detectChanges();

    const nameElement = fixture.debugElement.query(By.css('.info-item:nth-child(1) span'));
    const emailElement = fixture.debugElement.query(By.css('.info-item:nth-child(2) span'));
    const phoneElement = fixture.debugElement.query(By.css('.info-item:nth-child(3) span'));

    expect(nameElement.nativeElement.textContent.trim()).toBe('Test User');
    expect(emailElement.nativeElement.textContent.trim()).toBe('<EMAIL>');
    expect(phoneElement.nativeElement.textContent.trim()).toBe('+1234567890');
  });

  it('should display profile details when available', () => {
    fixture.detectChanges();

    const jobTitleElement = fixture.debugElement.query(By.css('.info-item span'));
    expect(jobTitleElement).toBeTruthy();
  });

  it('should display industry tags', () => {
    fixture.detectChanges();

    const tagsElements = fixture.debugElement.queryAll(By.css('.tag'));
    expect(tagsElements.length).toBe(2);
    expect(tagsElements[0].nativeElement.textContent.trim()).toBe('Technology');
    expect(tagsElements[1].nativeElement.textContent.trim()).toBe('Software Development');
  });

  it('should display organizations', () => {
    fixture.detectChanges();

    const orgNameElement = fixture.debugElement.query(By.css('.organization-card h3'));
    expect(orgNameElement.nativeElement.textContent.trim()).toBe('Test Organization');
  });

  it('should display roles and permissions', () => {
    fixture.detectChanges();

    const roleBadges = fixture.debugElement.queryAll(By.css('.role-badge'));
    expect(roleBadges.length).toBe(1);
    expect(roleBadges[0].nativeElement.textContent.trim()).toBe('USER');
  });

  it('should call refreshUserData when refresh button is clicked', () => {
    userDataServiceSpy.refreshUserData.and.returnValue(of(mockCompleteUser));
    fixture.detectChanges();

    const refreshButton = fixture.debugElement.query(By.css('.btn-primary'));
    refreshButton.nativeElement.click();

    expect(userDataServiceSpy.refreshUserData).toHaveBeenCalled();
  });

  it('should call updateProfile when update button is clicked', () => {
    const updateResponse = {
      message: 'Profile updated',
      user: mockCompleteUser
    };
    userDataServiceSpy.updateProfile.and.returnValue(of(updateResponse));
    fixture.detectChanges();

    const updateButton = fixture.debugElement.query(By.css('.btn-secondary'));
    updateButton.nativeElement.click();

    expect(userDataServiceSpy.updateProfile).toHaveBeenCalled();
  });

  it('should call deleteAccount when delete button is clicked and confirmed', () => {
    spyOn(window, 'confirm').and.returnValue(true);
    const deleteResponse = { message: 'Account deleted' };
    userDataServiceSpy.deleteAccount.and.returnValue(of(deleteResponse));
    fixture.detectChanges();

    const deleteButton = fixture.debugElement.query(By.css('.btn-danger'));
    deleteButton.nativeElement.click();

    expect(window.confirm).toHaveBeenCalled();
    expect(userDataServiceSpy.deleteAccount).toHaveBeenCalled();
  });

  it('should not call deleteAccount when delete is not confirmed', () => {
    spyOn(window, 'confirm').and.returnValue(false);
    fixture.detectChanges();

    const deleteButton = fixture.debugElement.query(By.css('.btn-danger'));
    deleteButton.nativeElement.click();

    expect(window.confirm).toHaveBeenCalled();
    expect(userDataServiceSpy.deleteAccount).not.toHaveBeenCalled();
  });

  it('should handle loading state', () => {
    Object.defineProperty(userDataServiceSpy, 'isLoading$', {
      value: of(true)
    });
    
    fixture.detectChanges();

    expect(component.isLoading).toBe(true);
    
    const loadingIndicator = fixture.debugElement.query(By.css('.loading-indicator'));
    expect(loadingIndicator).toBeTruthy();
  });

  it('should handle error when refreshing user data', () => {
    spyOn(console, 'error');
    userDataServiceSpy.refreshUserData.and.returnValue(throwError(() => new Error('Refresh failed')));
    
    component.refreshUserData();

    expect(console.error).toHaveBeenCalledWith('Failed to refresh user data:', jasmine.any(Error));
  });

  it('should handle error when updating profile', () => {
    spyOn(console, 'error');
    userDataServiceSpy.updateProfile.and.returnValue(throwError(() => new Error('Update failed')));
    
    component.updateProfile();

    expect(console.error).toHaveBeenCalledWith('Failed to update profile:', jasmine.any(Error));
  });

  it('should handle error when deleting account', () => {
    spyOn(window, 'confirm').and.returnValue(true);
    spyOn(console, 'error');
    userDataServiceSpy.deleteAccount.and.returnValue(throwError(() => new Error('Delete failed')));
    
    component.deleteAccount();

    expect(console.error).toHaveBeenCalledWith('Failed to delete account:', jasmine.any(Error));
  });

  it('should return correct user name', () => {
    component.completeUser = mockCompleteUser;
    expect(component.userName).toBe('Test User');

    component.completeUser = null;
    expect(component.userName).toBe('Unknown User');
  });

  it('should return correct user email', () => {
    component.completeUser = mockCompleteUser;
    expect(component.userEmail).toBe('<EMAIL>');

    component.completeUser = null;
    expect(component.userEmail).toBe('');
  });

  it('should return correct user bio', () => {
    component.completeUser = mockCompleteUser;
    expect(component.userBio).toBe('Experienced software developer with 5+ years in web development.');

    component.completeUser = null;
    expect(component.userBio).toBe('No bio available');
  });

  it('should return user organizations', () => {
    component.completeUser = mockCompleteUser;
    expect(component.userOrganizations).toEqual(mockCompleteUser.organizations);

    component.completeUser = null;
    expect(component.userOrganizations).toEqual([]);
  });

  it('should return user roles', () => {
    component.completeUser = mockCompleteUser;
    expect(component.userRoles).toEqual(['USER']);

    component.completeUser = null;
    expect(component.userRoles).toEqual([]);
  });

  it('should check if user has specific role', () => {
    component.completeUser = mockCompleteUser;
    expect(component.hasRole('USER')).toBe(true);
    expect(component.hasRole('ADMIN')).toBe(false);
  });

  it('should display no user data message when user is null', () => {
    Object.defineProperty(userDataServiceSpy, 'completeUser$', {
      value: of(null)
    });
    Object.defineProperty(userDataServiceSpy, 'isLoading$', {
      value: of(false)
    });

    fixture.detectChanges();

    const noDataElement = fixture.debugElement.query(By.css('.no-user-data'));
    expect(noDataElement).toBeTruthy();
    expect(noDataElement.nativeElement.textContent).toContain('No User Data Available');
  });

  it('should properly clean up subscriptions on destroy', () => {
    spyOn(component['destroy$'], 'next');
    spyOn(component['destroy$'], 'complete');

    component.ngOnDestroy();

    expect(component['destroy$'].next).toHaveBeenCalled();
    expect(component['destroy$'].complete).toHaveBeenCalled();
  });
});
