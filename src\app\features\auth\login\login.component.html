<div
  class="flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-gray-50"
>
  <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Sign in to your account
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Or
        <a
          routerLink="/auth/register"
          class="font-medium text-indigo-600 hover:text-indigo-500"
        >
          create a new account
        </a>
      </p>
    </div>

    <!-- Alert for errors -->
    <app-alert
      *ngIf="showAlert"
      [type]="alertType"
      [message]="alertMessage"
      [dismissible]="true"
    ></app-alert>

    <!-- Authentication Method Selection -->
    <div class="space-y-4">
      <div class="flex justify-center space-x-4">
        <button
          type="button"
          (click)="setAuthMethod('password')"
          [class]="
            'px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 ' +
            (authMethod === 'password'
              ? 'bg-indigo-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300')
          "
        >
          Password
        </button>
        <button
          type="button"
          (click)="setAuthMethod('otp')"
          [class]="
            'px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 ' +
            (authMethod === 'otp'
              ? 'bg-indigo-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300')
          "
        >
          OTP
        </button>
      </div>
    </div>

    <form
      class="mt-8 space-y-6"
      [formGroup]="loginForm"
      (ngSubmit)="onSubmit()"
    >
      <div class="space-y-4">
        <!-- Email/Phone Input (always visible) -->
        <div>
          <label
            for="identifier"
            class="block text-sm font-medium text-gray-700"
          >
            {{
              authMethod === "otp" ? "Phone Number" : "Email or Phone Number"
            }}
          </label>
          <input
            id="identifier"
            name="identifier"
            type="text"
            formControlName="identifier"
            autocomplete="username"
            [ngClass]="{
              'border-red-500': submitted && f['identifier'].errors
            }"
            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            [placeholder]="
              authMethod === 'otp'
                ? 'Enter your phone number'
                : 'Email or Phone Number'
            "
          />
          <div
            *ngIf="submitted && f['identifier'].errors"
            class="text-red-500 text-xs mt-1"
          >
            <div *ngIf="f['identifier'].errors['required']">
              {{
                authMethod === "otp"
                  ? "Phone number is required"
                  : "Email or phone number is required"
              }}
            </div>
          </div>
        </div>

        <!-- Password Input (only for password auth) -->
        <div *ngIf="authMethod === 'password'">
          <label for="password" class="block text-sm font-medium text-gray-700"
            >Password</label
          >
          <input
            id="password"
            name="password"
            type="password"
            formControlName="password"
            autocomplete="current-password"
            [ngClass]="{ 'border-red-500': submitted && f['password'].errors }"
            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Password"
          />
          <div
            *ngIf="submitted && f['password'].errors"
            class="text-red-500 text-xs mt-1"
          >
            <div *ngIf="f['password'].errors['required']">
              Password is required
            </div>
          </div>
        </div>

        <!-- OTP Input (only for OTP auth and after OTP is sent) -->
        <div *ngIf="authMethod === 'otp' && otpSent">
          <label for="otp" class="block text-sm font-medium text-gray-700"
            >Enter OTP Code</label
          >
          <input
            id="otp"
            name="otp"
            type="text"
            formControlName="otp"
            [ngClass]="{ 'border-red-500': submitted && f['otp'].errors }"
            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Enter 6-digit code"
            maxlength="6"
          />
          <div
            *ngIf="submitted && f['otp'].errors"
            class="text-red-500 text-xs mt-1"
          >
            <div *ngIf="f['otp'].errors['required']">OTP code is required</div>
            <div *ngIf="f['otp'].errors['minlength']">
              OTP code must be at least 6 digits
            </div>
          </div>
        </div>
      </div>

      <!-- Remember Me and Forgot Password (only for password auth) -->
      <div
        *ngIf="authMethod === 'password'"
        class="flex items-center justify-between"
      >
        <div class="flex items-center">
          <input
            id="remember-me"
            name="remember-me"
            type="checkbox"
            formControlName="rememberMe"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <label for="remember-me" class="ml-2 block text-sm text-gray-900">
            Remember me
          </label>
        </div>

        <div class="text-sm">
          <a
            routerLink="/auth/forgot-password"
            class="font-medium text-indigo-600 hover:text-indigo-500"
          >
            Forgot your password?
          </a>
        </div>
      </div>

      <div>
        <button
          type="submit"
          [disabled]="loading || loginForm.invalid"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span class="absolute left-0 inset-y-0 flex items-center pl-3">
            <svg
              *ngIf="!loading"
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-indigo-500 group-hover:text-indigo-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
            <svg
              *ngIf="loading"
              class="animate-spin h-5 w-5 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </span>
          <span *ngIf="!loading">{{ getSubmitButtonText() }}</span>
          <span *ngIf="loading">Processing...</span>
        </button>
      </div>
    </form>
  </div>
</div>
