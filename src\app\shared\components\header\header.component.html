<header class="w-full py-4 bg-[#0A0033] flex justify-center box-border">
  <div
    class="max-w-[1500px] w-full flex items-center justify-between px-6 md:px-10 box-border"
  >
    <!-- Logo Section -->
    <div class="flex flex-col items-center">
      <!-- <img src="assets/logo.png" alt="COMMSCLUB" class="w-auto h-auto" /> -->
      <h5 class="text-[13px] -mt-2 font-[Inter] font-normal text-white">
        LOGO
      </h5>
    </div>

    <!-- Desktop Navigation -->
    <nav
      class="hidden lg:flex items-center gap-[30px] font-[Inter] text-[17px] text-white"
    >
      <app-link href="/" linkText="Home" [linkUnderlineHover]="true"></app-link>

      <!-- <div class="relative group">
        <app-link
          href="/solutions"
          linkText="Solutions"
          [linkUnderlineHover]="true"
        ></app-link>
        <img
          src="assets/next.png"
          alt="Dropdown Arrow"
          class="inline-block w-[12px] h-auto align-middle cursor-pointer"
        />
      </div>

      <div class="relative group">
        <app-link
          href="/resources"
          linkText="Resources"
          [linkUnderlineHover]="true"
        ></app-link>
        <img
          src="assets/next.png"
          alt="Dropdown Arrow"
          class="inline-block w-[12px] h-auto align-middle"
        />
      </div>

      <app-link
        href="/expert-database"
        linkText="Expert Database"
        [linkUnderlineHover]="true"
      ></app-link> -->
      <span class="w-px h-[40px] bg-[#01C9F5] opacity-50"></span>
      <app-link
        href="/auth/login"
        linkText="Login"
        [linkUnderlineHover]="true"
      ></app-link>
      <app-button
        btnText="Sign up Free"
        btnSize="md"
        btnColor="gradient"
        link="/auth/register"
      ></app-button>
    </nav>

    <!-- Mobile Menu Button -->
    <div class="lg:hidden flex items-center">
      <button
        (click)="toggleMobileMenu()"
        class="text-white focus:outline-none"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-8 w-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16m-7 6h7"
          />
        </svg>
      </button>
    </div>
  </div>

  <!-- Mobile Navigation -->
  <div
    [class.hidden]="!isMobileMenuOpen"
    class="lg:hidden absolute top-[12vh] right-0 transform bg-[#0A0033] text-black shadow-md transition-all duration-300 ease-in-out w-full sm:w-auto sm:min-w-[250px] sm:max-w-[90%] rounded-b-lg p-b-4"
  >
    <nav class="flex flex-col items-center gap-4 py-4">
      <app-link href="/" linkText="Home" [linkUnderlineHover]="true"></app-link>
      <app-link
        href="/solutions"
        linkText="Solutions"
        [linkUnderlineHover]="true"
      ></app-link>
      <app-link
        href="/resources"
        linkText="Resources"
        [linkUnderlineHover]="true"
      ></app-link>
      <app-link
        href="/expert-database"
        linkText="Expert Database"
        [linkUnderlineHover]="true"
      ></app-link>
      <app-link
        href="/auth/login"
        linkText="Login"
        [linkUnderlineHover]="true"
      ></app-link>
      <app-button
        btnText="Sign up Free"
        btnSize="md"
        btnColor="gradient"
        link="/auth/register"
      ></app-button>
    </nav>
  </div>
</header>
