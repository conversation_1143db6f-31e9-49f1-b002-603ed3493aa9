import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { SubdomainValidationGuard } from './subdomain-validation.guard';
import { AuthService } from '../services/auth/auth.service';
import { UserDataService } from '../services/user-data/user-data.service';
import { SubdomainService } from '../services/subdomain/subdomain.service';
import { environment } from '../../../environments/environment';
import { CompleteUser } from '../models/user.model';

describe('SubdomainValidationGuard', () => {
  let guard: SubdomainValidationGuard;
  let authService: jasmine.SpyObj<AuthService>;
  let userDataService: jasmine.SpyObj<UserDataService>;
  let subdomainService: jasmine.SpyObj<SubdomainService>;
  let router: jasmine.SpyObj<Router>;

  const mockRoute = { routeConfig: { path: 'dashboard' } };

  const createMockCompleteUser = (
    type: 'individual' | 'organization',
    organizations: any[] = []
  ): CompleteUser => ({
    _id: '123',
    email: '<EMAIL>',
    type,
    status: 'active',
    profile: {
      fullName: 'Test User',
      jobTitle: 'Developer',
      companyName: 'Test Corp',
      bio: 'Test bio',
    },
    roles: [],
    systemPrivileges: [],
    organizations,
    privilegeSummary: {
      isGodSuperUser: false,
      hasSystemPrivileges: false,
      highestPrivilegeLevel: 0,
      organizationCount: organizations.length,
      hasDefaultOrganization: false,
    },
  });

  beforeEach(() => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', [
      'isAuthenticated',
      'getCompleteUser',
    ]);
    const userDataServiceSpy = jasmine.createSpyObj('UserDataService', [
      'refreshUserData',
    ]);
    const subdomainServiceSpy = jasmine.createSpyObj('SubdomainService', [
      'areSubdomainsEnabled',
      'getCurrentSubdomain',
    ]);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      providers: [
        SubdomainValidationGuard,
        { provide: AuthService, useValue: authServiceSpy },
        { provide: UserDataService, useValue: userDataServiceSpy },
        { provide: SubdomainService, useValue: subdomainServiceSpy },
        { provide: Router, useValue: routerSpy },
      ],
    });

    guard = TestBed.inject(SubdomainValidationGuard);
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    userDataService = TestBed.inject(
      UserDataService
    ) as jasmine.SpyObj<UserDataService>;
    subdomainService = TestBed.inject(
      SubdomainService
    ) as jasmine.SpyObj<SubdomainService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should redirect to login if user is not authenticated', (done) => {
    authService.isAuthenticated.and.returnValue(false);

    guard.canActivate(mockRoute).subscribe((result) => {
      expect(result).toBe(false);
      expect(router.navigate).toHaveBeenCalledWith(['/auth/login']);
      done();
    });
  });

  it('should allow access when subdomains are disabled', (done) => {
    authService.isAuthenticated.and.returnValue(true);
    subdomainService.areSubdomainsEnabled.and.returnValue(false);

    guard.canActivate(mockRoute).subscribe((result) => {
      expect(result).toBe(true);
      done();
    });
  });

  it('should validate user access to organization subdomain', (done) => {
    authService.isAuthenticated.and.returnValue(true);
    subdomainService.areSubdomainsEnabled.and.returnValue(true);
    subdomainService.getCurrentSubdomain.and.returnValue('testorg');

    const mockUser = createMockCompleteUser('organization', [
      { subdomain: 'testorg' },
    ]);
    authService.getCompleteUser.and.returnValue(mockUser);

    guard.canActivate(mockRoute).subscribe((result) => {
      expect(result).toBe(true);
      done();
    });
  });

  it('should deny access if user does not belong to the organization', (done) => {
    authService.isAuthenticated.and.returnValue(true);
    subdomainService.areSubdomainsEnabled.and.returnValue(true);
    subdomainService.getCurrentSubdomain.and.returnValue('wrongorg');

    const mockUser = createMockCompleteUser('organization', [
      { subdomain: 'testorg' },
    ]);
    authService.getCompleteUser.and.returnValue(mockUser);

    guard.canActivate(mockRoute).subscribe((result) => {
      expect(result).toBe(false);
      expect(router.navigate).toHaveBeenCalledWith(['/404']);
      done();
    });
  });

  it('should allow individual users access to main dashboard', (done) => {
    authService.isAuthenticated.and.returnValue(true);
    subdomainService.areSubdomainsEnabled.and.returnValue(true);
    subdomainService.getCurrentSubdomain.and.returnValue(null); // No subdomain

    const mockUser = createMockCompleteUser('individual', []);
    userDataService.refreshUserData.and.returnValue(of(mockUser));

    guard.canActivate(mockRoute).subscribe((result) => {
      expect(result).toBe(true);
      expect(router.navigate).not.toHaveBeenCalled();
      done();
    });
  });

  it('should redirect individual users from subdomain to 404', (done) => {
    authService.isAuthenticated.and.returnValue(true);
    subdomainService.areSubdomainsEnabled.and.returnValue(true);
    subdomainService.getCurrentSubdomain.and.returnValue('testorg'); // On subdomain

    const mockUser = createMockCompleteUser('individual', []);
    authService.getCompleteUser.and.returnValue(mockUser);

    guard.canActivate(mockRoute).subscribe((result) => {
      expect(result).toBe(false);
      expect(router.navigate).toHaveBeenCalledWith(['/404']);
      done();
    });
  });

  it('should handle errors gracefully', (done) => {
    authService.isAuthenticated.and.returnValue(true);
    subdomainService.areSubdomainsEnabled.and.returnValue(true);
    subdomainService.getCurrentSubdomain.and.returnValue(null);

    userDataService.refreshUserData.and.returnValue(
      throwError('Network error')
    );

    guard.canActivate(mockRoute).subscribe((result) => {
      expect(result).toBe(false);
      expect(router.navigate).toHaveBeenCalledWith(['/auth/login']);
      done();
    });
  });
});
