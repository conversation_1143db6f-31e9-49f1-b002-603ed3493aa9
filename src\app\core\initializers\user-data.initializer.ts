import { APP_INITIALIZER } from '@angular/core';
import { UserDataService } from '../services/user-data/user-data.service';
import { SubdomainService } from '../services/subdomain/subdomain.service';
import { Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export function initializeUserData(
  userDataService: UserDataService,
  subdomainService: SubdomainService
) {
  return (): Observable<any> => {
    console.log('Initializing user data...');

    // Check if we're on a subdomain-login route to avoid CORS issues during token transfer
    const currentPath = window.location.pathname;
    const currentSubdomain = subdomainService.getCurrentSubdomain();
    const isSubdomainLoginRoute = currentPath.includes('/auth/subdomain-login');

    if (
      currentSubdomain &&
      isSubdomainLoginRoute &&
      environment.enableSubdomains
    ) {
      console.log(
        'UserDataInitializer: On subdomain-login route, skipping API call to avoid CORS issues during token transfer'
      );
      console.log(
        'UserDataInitializer: User data will be loaded after token transfer completes'
      );
      return of(null);
    }

    console.log(
      'UserDataInitializer: Not on subdomain-login route, proceeding with user data initialization'
    );

    return userDataService.initializeUserData().pipe(
      tap((completeUser) => {
        if (completeUser) {
          console.log('User data initialized successfully:', completeUser.name);
        } else {
          console.log('No authenticated user found');
        }
      }),
      catchError((error) => {
        console.warn('Failed to initialize user data:', error);
        // Don't fail app initialization if user data loading fails
        return of(null);
      })
    );
  };
}

export const USER_DATA_INITIALIZER = {
  provide: APP_INITIALIZER,
  useFactory: initializeUserData,
  deps: [UserDataService, SubdomainService],
  multi: true,
};
