import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, timer } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { UserDataService } from '../../../core/services/user-data/user-data.service';
import { AuthService } from '../../../core/services/auth/auth.service';

@Component({
  selector: 'app-subdomain-loading',
  templateUrl: './subdomain-loading.component.html',
  styleUrls: ['./subdomain-loading.component.css']
})
export class SubdomainLoadingComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  loadingMessage = 'Initializing your organization dashboard...';
  progress = 0;
  showRetryButton = false;
  
  private readonly INITIAL_DELAY = 2000; // 2 seconds initial delay
  private readonly MAX_WAIT_TIME = 15000; // 15 seconds max wait
  private readonly PROGRESS_INTERVAL = 100; // Update progress every 100ms

  constructor(
    private userDataService: UserDataService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.startLoadingSequence();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private startLoadingSequence(): void {
    console.log('SubdomainLoadingComponent: Starting loading sequence');
    
    // Start progress animation
    this.animateProgress();
    
    // Wait for initial delay to let subdomain authentication settle
    timer(this.INITIAL_DELAY).pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.loadingMessage = 'Loading your profile data...';
      this.attemptDataLoad();
    });
    
    // Set maximum wait time
    timer(this.MAX_WAIT_TIME).pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      if (!this.showRetryButton) {
        this.handleLoadingTimeout();
      }
    });
  }

  private animateProgress(): void {
    const progressTimer = timer(0, this.PROGRESS_INTERVAL).pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      if (this.progress < 90) {
        // Slow down progress as it approaches completion
        const increment = this.progress < 50 ? 2 : this.progress < 80 ? 1 : 0.5;
        this.progress = Math.min(this.progress + increment, 90);
      }
    });
  }

  private attemptDataLoad(): void {
    console.log('SubdomainLoadingComponent: Attempting to load user data');
    
    // Check if we already have cached user data
    const cachedUser = this.authService.getCompleteUser();
    if (cachedUser) {
      console.log('SubdomainLoadingComponent: Found cached user data, proceeding');
      this.completeLoading();
      return;
    }

    // Try to load user data
    this.userDataService.loadCompleteUserData().subscribe({
      next: (userData) => {
        console.log('SubdomainLoadingComponent: Successfully loaded user data');
        this.completeLoading();
      },
      error: (error) => {
        console.error('SubdomainLoadingComponent: Failed to load user data:', error);
        this.handleLoadingError(error);
      }
    });
  }

  private completeLoading(): void {
    console.log('SubdomainLoadingComponent: Loading complete, navigating to dashboard');
    this.progress = 100;
    this.loadingMessage = 'Ready! Redirecting to dashboard...';
    
    // Small delay to show completion
    timer(500).pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.router.navigate(['/org-dashboard']);
    });
  }

  private handleLoadingError(error: any): void {
    console.error('SubdomainLoadingComponent: Loading error:', error);
    
    // Check if it's a CORS error
    if (this.isCorsError(error)) {
      this.loadingMessage = 'Connection issue detected. Preparing to retry...';
      
      // Wait a bit longer and try again
      timer(3000).pipe(
        takeUntil(this.destroy$)
      ).subscribe(() => {
        this.attemptDataLoad();
      });
    } else {
      this.showRetryOptions();
    }
  }

  private handleLoadingTimeout(): void {
    console.warn('SubdomainLoadingComponent: Loading timeout reached');
    this.showRetryOptions();
  }

  private showRetryOptions(): void {
    this.showRetryButton = true;
    this.loadingMessage = 'Taking longer than expected...';
    this.progress = 100;
  }

  private isCorsError(error: any): boolean {
    return (
      error &&
      (error.status === 0 ||
        error.message?.includes('CORS') ||
        error.message?.includes('Access-Control-Allow-Origin') ||
        error.name === 'SubdomainCorsError')
    );
  }

  retryLoading(): void {
    console.log('SubdomainLoadingComponent: User requested retry');
    this.showRetryButton = false;
    this.progress = 0;
    this.loadingMessage = 'Retrying...';
    this.startLoadingSequence();
  }

  forceRefresh(): void {
    console.log('SubdomainLoadingComponent: User requested page refresh');
    window.location.reload();
  }

  goToDashboard(): void {
    console.log('SubdomainLoadingComponent: User chose to proceed to dashboard');
    this.router.navigate(['/org-dashboard']);
  }
}
