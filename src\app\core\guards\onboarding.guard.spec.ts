import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

import { OnboardingGuard } from './onboarding.guard';
import { AuthService } from '../services/auth/auth.service';
import { UserDataService } from '../services/user-data/user-data.service';
import { CompleteUser } from '../models/user.model';

describe('OnboardingGuard', () => {
  let guard: OnboardingGuard;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let userDataServiceSpy: jasmine.SpyObj<UserDataService>;
  let routerSpy: jasmine.SpyObj<Router>;

  const mockCompleteUser: CompleteUser = {
    _id: '123',
    name: 'Test User',
    email: '<EMAIL>',
    phone_number: '+1234567890',
    type: 'individual',
    status: 'active',
    profile: {
      fullName: 'Test User Full',
      jobTitle: 'Developer',
      companyName: 'Test Corp',
    },
    roles: [],
    systemPrivileges: [],
    organizations: [],
    privilegeSummary: {
      isGodSuperUser: false,
      hasSystemPrivileges: false,
      highestPrivilegeLevel: 0,
      organizationCount: 0,
    },
  };

  beforeEach(() => {
    const authSpy = jasmine.createSpyObj('AuthService', ['isAuthenticated', 'isOnboardingCompleted']);
    const userDataSpy = jasmine.createSpyObj('UserDataService', ['loadCompleteUserData']);
    const routerSpyObj = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      providers: [
        OnboardingGuard,
        { provide: AuthService, useValue: authSpy },
        { provide: UserDataService, useValue: userDataSpy },
        { provide: Router, useValue: routerSpyObj }
      ]
    });

    guard = TestBed.inject(OnboardingGuard);
    authServiceSpy = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    userDataServiceSpy = TestBed.inject(UserDataService) as jasmine.SpyObj<UserDataService>;
    routerSpy = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should redirect to login if user is not authenticated', () => {
    authServiceSpy.isAuthenticated.and.returnValue(false);

    guard.canActivate().subscribe(result => {
      expect(result).toBe(false);
      expect(routerSpy.navigate).toHaveBeenCalledWith(['/auth/login']);
    });
  });

  it('should redirect to onboarding if user has not completed onboarding', () => {
    authServiceSpy.isAuthenticated.and.returnValue(true);
    authServiceSpy.isOnboardingCompleted.and.returnValue(false);
    userDataServiceSpy.loadCompleteUserData.and.returnValue(of(mockCompleteUser));

    guard.canActivate().subscribe(result => {
      expect(result).toBe(false);
      expect(routerSpy.navigate).toHaveBeenCalledWith(['/onboarding']);
    });
  });

  it('should allow access if user has completed onboarding', () => {
    authServiceSpy.isAuthenticated.and.returnValue(true);
    authServiceSpy.isOnboardingCompleted.and.returnValue(true);
    userDataServiceSpy.loadCompleteUserData.and.returnValue(of(mockCompleteUser));

    guard.canActivate().subscribe(result => {
      expect(result).toBe(true);
      expect(routerSpy.navigate).not.toHaveBeenCalled();
    });
  });

  it('should redirect to login on error loading user data', () => {
    authServiceSpy.isAuthenticated.and.returnValue(true);
    userDataServiceSpy.loadCompleteUserData.and.returnValue(throwError(() => new Error('API Error')));

    guard.canActivate().subscribe(result => {
      expect(result).toBe(false);
      expect(routerSpy.navigate).toHaveBeenCalledWith(['/auth/login']);
    });
  });
});
