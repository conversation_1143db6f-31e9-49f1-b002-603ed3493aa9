import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth/auth.service';
import { UserDataService } from '../services/user-data/user-data.service';

@Injectable({
  providedIn: 'root',
})
export class OnboardingGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private userDataService: UserDataService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean> {
    console.log('OnboardingGuard: Checking onboarding status...');

    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      console.log(
        'OnboardingGuard: User not authenticated, redirecting to login'
      );
      this.router.navigate(['/auth/login']);
      return of(false);
    }

    // Force refresh of user data to get the latest information
    console.log('OnboardingGuard: Loading fresh user data...');
    return this.userDataService.refreshUserData().pipe(
      map((completeUser) => {
        console.log('OnboardingGuard: Fresh user data loaded:', {
          userId: completeUser?._id,
          type: completeUser?.type,
          hasProfile: !!completeUser?.profile,
          profileFields: completeUser?.profile
            ? Object.keys(completeUser.profile)
            : [],
          organizationCount: completeUser?.organizations?.length || 0,
        });

        if (!completeUser) {
          console.log(
            'OnboardingGuard: No user data found, redirecting to login'
          );
          this.router.navigate(['/auth/login']);
          return false;
        }

        // Check if onboarding is completed using fresh data
        if (!this.authService.isOnboardingCompletedForUser(completeUser)) {
          console.log(
            'OnboardingGuard: Onboarding not completed, redirecting to onboarding'
          );
          this.router.navigate(['/onboarding']);
          return false;
        }

        console.log('OnboardingGuard: Onboarding completed, allowing access');
        return true;
      }),
      catchError((error) => {
        console.error(
          'OnboardingGuard: Error checking onboarding status:',
          error
        );
        this.router.navigate(['/auth/login']);
        return of(false);
      })
    );
  }
}
