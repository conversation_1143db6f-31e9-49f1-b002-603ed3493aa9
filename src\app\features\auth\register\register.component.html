<div
  class="flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-gray-50"
>
  <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        {{ isLoginMode ? "Sign in to your account" : "Create your account" }}
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        {{ isLoginMode ? "Or" : "Already have an account?" }}
        <button
          type="button"
          (click)="toggleMode()"
          class="font-medium text-indigo-600 hover:text-indigo-500 ml-1"
        >
          {{ isLoginMode ? "create a new account" : "sign in here" }}
        </button>
      </p>
    </div>

    <!-- Alert for errors -->
    <app-alert
      *ngIf="showAlert"
      [type]="alertType"
      [message]="alertMessage"
      [dismissible]="true"
    ></app-alert>

    <!-- Authentication Method Selection -->
    <div class="space-y-4">
      <div class="flex justify-center space-x-4">
        <button
          type="button"
          (click)="setAuthMethod('password')"
          [class]="getAuthMethodButtonClass('password')"
        >
          Password
        </button>
        <button
          type="button"
          (click)="setAuthMethod('otp')"
          [class]="getAuthMethodButtonClass('otp')"
        >
          OTP
        </button>
      </div>
    </div>

    <!-- Authentication Forms -->
    <form [formGroup]="authForm" (ngSubmit)="onSubmit()" class="mt-8 space-y-6">
      <div class="space-y-4">
        <!-- Email/Phone Input (always visible) -->
        <div>
          <div>
            <label
              for="identifier"
              class="block text-sm font-medium text-gray-700"
            >
              {{
                authMethod === "otp" ? "Phone Number" : "Email or Phone Number"
              }}
            </label>
            <input
              id="identifier"
              name="identifier"
              type="text"
              formControlName="identifier"
              autocomplete="username"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              [placeholder]="
                authMethod === 'otp'
                  ? 'Enter your phone number'
                  : 'Email or Phone Number'
              "
            />
            <div
              *ngIf="
                authForm.get('identifier')?.invalid &&
                authForm.get('identifier')?.touched
              "
              class="mt-1 text-sm text-red-600"
            >
              {{
                authMethod === "otp"
                  ? "Phone number is required"
                  : "Email or phone number is required"
              }}
            </div>
          </div>
        </div>

        <!-- Password Input (only for password auth) -->
        <div *ngIf="authMethod === 'password'">
          <label for="password" class="block text-sm font-medium text-gray-700"
            >Password</label
          >
          <input
            id="password"
            name="password"
            type="password"
            formControlName="password"
            autocomplete="current-password"
            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Password"
          />
          <div
            *ngIf="
              authForm.get('password')?.invalid &&
              authForm.get('password')?.touched
            "
            class="mt-1 text-sm text-red-600"
          >
            Password is required (minimum 6 characters)
          </div>
        </div>

        <!-- Name field for registration -->
        <div *ngIf="!isLoginMode">
          <label for="name" class="block text-sm font-medium text-gray-700">
            Full Name
          </label>
          <input
            id="name"
            name="name"
            type="text"
            formControlName="name"
            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Full Name"
          />
          <div
            *ngIf="
              authForm.get('name')?.invalid && authForm.get('name')?.touched
            "
            class="mt-1 text-sm text-red-600"
          >
            Name is required
          </div>
        </div>
      </div>

      <!-- OTP Input (only for OTP auth and after OTP is sent) -->
      <div *ngIf="authMethod === 'otp' && otpSent">
        <div>
          <label for="otp" class="block text-sm font-medium text-gray-700"
            >Enter OTP Code</label
          >
          <input
            id="otp"
            name="otp"
            type="text"
            formControlName="otp"
            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Enter 6-digit code"
            maxlength="6"
          />
          <div
            *ngIf="authForm.get('otp')?.invalid && authForm.get('otp')?.touched"
            class="mt-1 text-sm text-red-600"
          >
            <div *ngIf="authForm.get('otp')?.errors?.['required']">
              OTP code is required
            </div>
            <div *ngIf="authForm.get('otp')?.errors?.['minlength']">
              OTP code must be at least 6 digits
            </div>
          </div>
        </div>

        <div>
          <label
            for="phone-password"
            class="block text-sm font-medium text-gray-700"
          >
            Password
          </label>
          <input
            id="phone-password"
            name="phone-password"
            type="password"
            formControlName="password"
            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Password"
          />
          <div
            *ngIf="
              authForm.get('password')?.invalid &&
              authForm.get('password')?.touched
            "
            class="mt-1 text-sm text-red-600"
          >
            Password is required (minimum 6 characters)
          </div>
        </div>

        <!-- Name field for registration -->
        <div *ngIf="!isLoginMode">
          <label
            for="phone-name"
            class="block text-sm font-medium text-gray-700"
          >
            Full Namee
          </label>
          <input
            id="phone-name"
            name="phone-name"
            type="text"
            formControlName="name"
            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Full Name"
          />
          <div
            *ngIf="
              authForm.get('name')?.invalid && authForm.get('name')?.touched
            "
            class="mt-1 text-sm text-red-600"
          >
            Name is required*
          </div>
        </div>
      </div>

      <!-- Submit Button -->
      <div>
        <button
          type="submit"
          [disabled]="loading || authForm.invalid"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span
            *ngIf="loading"
            class="absolute left-0 inset-y-0 flex items-center pl-3"
          >
            <svg
              class="animate-spin h-5 w-5 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </span>
          {{ getSubmitButtonText() }}
        </button>
      </div>

      <!-- Forgot Password Link -->
      <div *ngIf="isLoginMode" class="text-center">
        <a
          routerLink="/auth/forgot-password"
          class="text-sm text-indigo-600 hover:text-indigo-500"
        >
          Forgot your password?
        </a>
      </div>
    </form>
  </div>
</div>
