import { Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../auth/auth.service';

@Injectable({
  providedIn: 'root',
})
export class SubdomainService {
  constructor(private authService: AuthService) {}

  /**
   * Extract subdomain from current host
   */
  getCurrentSubdomain(): string | null {
    console.log('SubdomainService: getCurrentSubdomain called');
    console.log(
      'SubdomainService: enableSubdomains =',
      environment.enableSubdomains
    );

    if (!environment.enableSubdomains) {
      console.log('SubdomainService: Subdomains disabled, returning null');
      return null;
    }

    const currentHost = window.location.host;
    console.log('SubdomainService: Current host =', currentHost);
    const subdomain = this.extractSubdomainFromHost(currentHost);
    console.log('SubdomainService: Extracted subdomain =', subdomain);
    return subdomain;
  }

  /**
   * Extract subdomain from a given host
   */
  extractSubdomainFromHost(host: string): string | null {
    console.log(
      'SubdomainService: extractSubdomainFromHost called with host =',
      host
    );

    if (!environment.enableSubdomains) {
      console.log(
        'SubdomainService: Subdomains disabled in extractSubdomainFromHost'
      );
      return null;
    }

    const pattern = environment.subdomainPattern;
    console.log('SubdomainService: Using pattern =', pattern);

    if (pattern.includes('{subdomain}')) {
      // For patterns like '{subdomain}.digimeet.live'
      const basePattern = pattern.replace('{subdomain}', '');
      console.log('SubdomainService: Base pattern =', basePattern);

      if (host.endsWith(basePattern)) {
        const subdomain = host.replace(basePattern, '');
        console.log('SubdomainService: Extracted subdomain =', subdomain);
        return subdomain || null;
      } else {
        console.log('SubdomainService: Host does not match pattern');
      }
    } else {
      console.log('SubdomainService: Pattern does not contain {subdomain}');
    }

    console.log('SubdomainService: No subdomain found, returning null');
    return null;
  }

  /**
   * Generate subdomain URL for a given subdomain
   */
  generateSubdomainUrl(subdomain: string, path: string = ''): string {
    if (!environment.enableSubdomains) {
      throw new Error('Subdomains are not enabled in current environment');
    }

    const subdomainHost = environment.subdomainPattern.replace(
      '{subdomain}',
      subdomain
    );
    const protocol = window.location.protocol;
    const fullPath = path.startsWith('/') ? path : `/${path}`;

    return `${protocol}//${subdomainHost}${fullPath}`;
  }

  /**
   * Check if current host matches expected subdomain
   */
  isOnCorrectSubdomain(expectedSubdomain: string): boolean {
    if (!environment.enableSubdomains) {
      return true; // In development mode, consider it correct
    }

    const currentSubdomain = this.getCurrentSubdomain();
    return currentSubdomain === expectedSubdomain;
  }

  /**
   * Redirect to subdomain URL
   */
  redirectToSubdomain(
    subdomain: string,
    path: string = '/org-dashboard'
  ): void {
    if (!environment.enableSubdomains) {
      console.warn('Cannot redirect to subdomain: subdomains are disabled');
      return;
    }

    // Get current auth tokens
    const accessToken = this.authService.getAccessToken();
    const refreshToken = this.authService.getRefreshToken();

    if (!accessToken || !refreshToken) {
      console.error('No auth tokens available for subdomain redirect');
      // Fallback to direct redirect without tokens
      const subdomainUrl = this.generateSubdomainUrl(subdomain, path);
      console.log('Redirecting to subdomain URL without tokens:', subdomainUrl);
      window.location.href = subdomainUrl;
      return;
    }

    // Create subdomain URL with tokens for authentication
    const subdomainHost = environment.subdomainPattern.replace(
      '{subdomain}',
      subdomain
    );
    const protocol = window.location.protocol;
    // Ensure we're redirecting to org-dashboard, not regular dashboard
    const redirectPath = path.startsWith('/') ? path.substring(1) : path;
    const finalRedirectPath =
      redirectPath === 'dashboard' ? 'org-dashboard' : redirectPath;
    const subdomainUrl = `${protocol}//${subdomainHost}/auth/subdomain-login?token=${encodeURIComponent(
      accessToken
    )}&refresh=${encodeURIComponent(
      refreshToken
    )}&redirect=${finalRedirectPath}`;

    console.log('Redirecting to subdomain URL with tokens:', subdomainUrl);
    window.location.href = subdomainUrl;
  }

  /**
   * Check if subdomains are enabled in current environment
   */
  areSubdomainsEnabled(): boolean {
    return environment.enableSubdomains;
  }

  /**
   * Get the base domain (without subdomain)
   */
  getBaseDomain(): string {
    if (!environment.enableSubdomains) {
      return window.location.host;
    }

    const pattern = environment.subdomainPattern;
    return pattern.replace('{subdomain}.', '');
  }

  /**
   * Validate if user has access to a specific subdomain
   * Uses cached user data from localStorage for fast validation
   */
  validateUserSubdomainAccess(subdomain: string): boolean {
    const completeUser = this.authService.getCompleteUser();

    if (!completeUser) {
      console.log('SubdomainService: No user data available for validation');
      return false;
    }

    if (completeUser.type !== 'organization') {
      console.log(
        'SubdomainService: Individual user cannot access organization subdomains'
      );
      return false;
    }

    const userOrgs = completeUser.organizations || [];
    const hasAccess = userOrgs.some((org: any) => org.subdomain === subdomain);

    console.log('SubdomainService: Subdomain access validation:', {
      subdomain,
      userEmail: completeUser.email,
      userOrganizations: userOrgs.map((org: any) => org.subdomain),
      hasAccess,
    });

    return hasAccess;
  }

  /**
   * Get all subdomains the current user has access to
   * Returns default organization subdomain first, then others
   */
  getUserSubdomains(): string[] {
    const completeUser = this.authService.getCompleteUser();

    if (!completeUser || completeUser.type !== 'organization') {
      return [];
    }

    const subdomains: string[] = [];

    // Add default organization subdomain first
    if (
      completeUser.defaultOrganization &&
      completeUser.defaultOrganization.subdomain
    ) {
      subdomains.push(completeUser.defaultOrganization.subdomain);
    }

    // Add other organization subdomains (excluding default to avoid duplicates)
    const userOrgs = completeUser.organizations || [];
    const defaultSubdomain = completeUser.defaultOrganization?.subdomain;

    userOrgs.forEach((org: any) => {
      if (org.subdomain && org.subdomain !== defaultSubdomain) {
        subdomains.push(org.subdomain);
      }
    });

    return subdomains.filter(Boolean);
  }

  /**
   * Get the user's primary (default) organization
   */
  getPrimaryOrganization(): any {
    const completeUser = this.authService.getCompleteUser();

    if (!completeUser || completeUser.type !== 'organization') {
      return null;
    }

    // Return default organization if available, otherwise first organization
    if (completeUser.defaultOrganization) {
      return completeUser.defaultOrganization;
    }

    const userOrgs = completeUser.organizations || [];
    return userOrgs.length > 0 ? userOrgs[0] : null;
  }
}
