import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { OrganizationDataService } from '../../../../core/services/organization-data/organization-data.service';
import { UserDataService } from '../../../../core/services/user-data/user-data.service';
import { OrganizationProfileSetupComponent } from './organization-profile-setup.component';

describe('OrganizationProfileSetupComponent', () => {
  let component: OrganizationProfileSetupComponent;
  let fixture: ComponentFixture<OrganizationProfileSetupComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockOrganizationDataService: jasmine.SpyObj<OrganizationDataService>;
  let mockUserDataService: jasmine.SpyObj<UserDataService>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const orgDataServiceSpy = jasmine.createSpyObj('OrganizationDataService', ['createOrganizationByUser']);
    const userDataServiceSpy = jasmine.createSpyObj('UserDataService', ['updateProfile']);

    await TestBed.configureTestingModule({
      declarations: [OrganizationProfileSetupComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: OrganizationDataService, useValue: orgDataServiceSpy },
        { provide: UserDataService, useValue: userDataServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(OrganizationProfileSetupComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockOrganizationDataService = TestBed.inject(OrganizationDataService) as jasmine.SpyObj<OrganizationDataService>;
    mockUserDataService = TestBed.inject(UserDataService) as jasmine.SpyObj<UserDataService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with step 1', () => {
    expect(component.currentStep).toBe(1);
  });

  it('should generate correct subdomain preview', () => {
    component.organizationForm.patchValue({ subdomain: 'testcompany' });
    expect(component.subdomainPreview).toBe('testcompany.digimeet.io');
  });

  it('should clean subdomain on change', () => {
    component.organizationForm.patchValue({ subdomain: 'Test-Company!' });
    component.onSubdomainChange();
    expect(component.organizationForm.get('subdomain')?.value).toBe('test-company');
  });

  it('should add and remove roles correctly', () => {
    component.onRoleChange('sponsor', true);
    expect(component.isRoleSelected('sponsor')).toBe(true);
    
    component.onRoleChange('sponsor', false);
    expect(component.isRoleSelected('sponsor')).toBe(false);
  });

  it('should add team invite', () => {
    component.teamForm.patchValue({
      memberEmail: '<EMAIL>',
      memberRole: 'admin'
    });
    
    component.addTeamInvite();
    
    expect(component.teamInvites.length).toBe(1);
    expect(component.teamInvites[0].email).toBe('<EMAIL>');
    expect(component.teamInvites[0].role).toBe('admin');
  });

  it('should remove team invite', () => {
    component.teamInvites = [
      { email: '<EMAIL>', role: 'admin' }
    ];
    
    component.removeTeamInvite(0);
    
    expect(component.teamInvites.length).toBe(0);
  });

  it('should create organization successfully', () => {
    const mockResponse = {
      organization: { _id: 'org123', name: 'Test Org' }
    };
    mockOrganizationDataService.createOrganizationByUser.and.returnValue(of(mockResponse));
    
    component.organizationForm.patchValue({
      displayName: 'Test Organization',
      subdomain: 'testorg',
      industry: 'Technology',
      contactEmail: '<EMAIL>',
      roles: ['sponsor']
    });

    component.nextStep();

    expect(mockOrganizationDataService.createOrganizationByUser).toHaveBeenCalled();
    expect(component.currentStep).toBe(2);
    expect(component.organizationId).toBe('org123');
  });

  it('should handle organization creation error', () => {
    mockOrganizationDataService.createOrganizationByUser.and.returnValue(
      throwError({ message: 'Creation failed' })
    );
    
    component.organizationForm.patchValue({
      displayName: 'Test Organization',
      subdomain: 'testorg',
      industry: 'Technology',
      contactEmail: '<EMAIL>',
      roles: ['sponsor']
    });

    component.nextStep();

    expect(component.showAlert).toBe(true);
    expect(component.alertType).toBe('error');
    expect(component.currentStep).toBe(1);
  });

  it('should complete setup successfully', () => {
    mockUserDataService.updateProfile.and.returnValue(of({}));
    
    component.completeSetup();

    expect(mockUserDataService.updateProfile).toHaveBeenCalledWith({ type: 'organization' });
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);
  });
});
