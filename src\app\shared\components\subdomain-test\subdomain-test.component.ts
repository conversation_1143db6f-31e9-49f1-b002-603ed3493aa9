import { Component, OnInit } from '@angular/core';
import { SubdomainService } from '../../../core/services/subdomain/subdomain.service';
import { AuthService } from '../../../core/services/auth/auth.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-subdomain-test',
  template: `
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <h3 class="font-bold text-lg mb-2">🚨 Subdomain Security Test</h3>
      
      <div class="space-y-2 text-sm">
        <div><strong>Current Host:</strong> {{ currentHost }}</div>
        <div><strong>Environment:</strong> {{ environment.production ? 'Production' : 'Development' }}</div>
        <div><strong>Subdomains Enabled:</strong> {{ environment.enableSubdomains }}</div>
        <div><strong>Subdomain Pattern:</strong> {{ environment.subdomainPattern }}</div>
        <div><strong>App Domain:</strong> {{ environment.appDomain }}</div>
        
        <hr class="my-2">
        
        <div><strong>Extracted Subdomain:</strong> {{ extractedSubdomain || 'None' }}</div>
        <div><strong>User Type:</strong> {{ userType || 'Not logged in' }}</div>
        <div><strong>User Organizations:</strong> {{ userOrganizations.length }}</div>
        
        <hr class="my-2">
        
        <div class="font-semibold">
          <strong>Access Validation:</strong> 
          <span [class]="hasAccess ? 'text-green-600' : 'text-red-600'">
            {{ hasAccess ? '✅ ALLOWED' : '❌ BLOCKED' }}
          </span>
        </div>
        
        <div *ngIf="!hasAccess && extractedSubdomain" class="text-red-600 font-semibold">
          ⚠️ This subdomain should redirect to 404!
        </div>
        
        <div class="mt-2">
          <strong>Valid Subdomains:</strong>
          <ul class="list-disc list-inside ml-4">
            <li *ngFor="let subdomain of validSubdomains">{{ subdomain }}</li>
          </ul>
        </div>
      </div>
      
      <div class="mt-4">
        <h4 class="font-semibold mb-2">Test Cases:</h4>
        <div class="space-y-1 text-xs">
          <div>✅ testorg4.digimeet.live → Should work (if in user's orgs)</div>
          <div>❌ testorg4guuojdf.digimeet.live → Should redirect to 404</div>
          <div>❌ nonexistent.digimeet.live → Should redirect to 404</div>
        </div>
      </div>
    </div>
  `,
  styles: []
})
export class SubdomainTestComponent implements OnInit {
  environment = environment;
  currentHost = '';
  extractedSubdomain: string | null = null;
  userType: string | null = null;
  userOrganizations: any[] = [];
  validSubdomains: string[] = [];
  hasAccess = false;

  constructor(
    private subdomainService: SubdomainService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadTestData();
  }

  private loadTestData(): void {
    // Get current host and extracted subdomain
    this.currentHost = window.location.host;
    this.extractedSubdomain = this.subdomainService.getCurrentSubdomain();
    
    console.log('SubdomainTest: Current host:', this.currentHost);
    console.log('SubdomainTest: Extracted subdomain:', this.extractedSubdomain);
    
    // Test subdomain extraction with various inputs
    this.testSubdomainExtraction();
    
    // Get user data
    const completeUser = this.authService.getCompleteUser();
    if (completeUser) {
      this.userType = completeUser.type;
      this.userOrganizations = completeUser.organizations || [];
      this.validSubdomains = this.userOrganizations.map(org => org.subdomain);
      
      // Test access validation
      if (this.extractedSubdomain) {
        this.hasAccess = this.subdomainService.validateUserSubdomainAccess(this.extractedSubdomain);
      }
    }
  }
  
  private testSubdomainExtraction(): void {
    const testCases = [
      'testorg4.digimeet.live',
      'testorg4guuojdf.digimeet.live',
      'aassa.digimeet.live',
      'iwillqqq.digimeet.live',
      'nonexistent.digimeet.live',
      'digimeet.live',
      'www.digimeet.live'
    ];
    
    console.log('SubdomainTest: Testing subdomain extraction:');
    testCases.forEach(host => {
      const extracted = this.subdomainService.extractSubdomainFromHost(host);
      console.log(`  ${host} → ${extracted || 'null'}`);
    });
  }
}
