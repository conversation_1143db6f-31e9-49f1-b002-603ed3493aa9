import { TestBed } from '@angular/core/testing';
import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { environment } from '../../../../environments/environment';

import { AuthService } from './auth.service';
import {
  User,
  CompleteUser,
  UserCredentials,
  RegistrationData,
  AuthResponse,
  PasswordResetRequest,
  PasswordResetConfirm,
  EmailOtpRequest,
  EmailOtpVerify,
  PhoneOtpRequest,
  PhoneOtpVerify,
} from '../../models/user.model';

describe('AuthService', () => {
  let service: AuthService;
  let httpMock: HttpTestingController;

  const mockUser: User = {
    _id: '123',
    JWT_UID: 'jwt123',
    email: '<EMAIL>',
    name: 'Test User',
    phone_number: '+1234567890',
    roles: [],
  };

  const mockCompleteUser: CompleteUser = {
    _id: '123',
    name: 'Test User',
    email: '<EMAIL>',
    phone_number: '+1234567890',
    type: 'individual',
    status: 'active',
    profile: {
      fullName: 'Test User Full',
      jobTitle: 'Developer',
      companyName: 'Test Corp',
      bio: 'Test bio',
    },
    roles: [],
    systemPrivileges: [],
    organizations: [],
    privilegeSummary: {
      isGodSuperUser: false,
      hasSystemPrivileges: false,
      highestPrivilegeLevel: 0,
      organizationCount: 0,
    },
  };

  const mockAuthResponse: AuthResponse = {
    message: 'Login successful',
    user: mockUser,
    access_token: 'mock-access-token',
    refresh_token: 'mock-refresh-token',
    expires_in: '3600',
    token_type: 'Bearer',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [AuthService],
    });

    service = TestBed.inject(AuthService);
    httpMock = TestBed.inject(HttpTestingController);

    // Clear localStorage before each test
    localStorage.clear();
  });

  afterEach(() => {
    httpMock.verify();
    localStorage.clear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getCurrentUser', () => {
    it('should return current user from subject', () => {
      service['currentUserSubject'].next(mockUser);

      const result = service.getCurrentUser();

      expect(result).toEqual(mockUser);
    });

    it('should return null when no user is set', () => {
      const result = service.getCurrentUser();

      expect(result).toBeNull();
    });
  });

  describe('getCompleteUser', () => {
    it('should return complete user from subject', () => {
      service['completeUserSubject'].next(mockCompleteUser);

      const result = service.getCompleteUser();

      expect(result).toEqual(mockCompleteUser);
    });

    it('should return null when no complete user is set', () => {
      const result = service.getCompleteUser();

      expect(result).toBeNull();
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when access token exists', () => {
      localStorage.setItem('access_token', 'mock-token');

      const result = service.isAuthenticated();

      expect(result).toBe(true);
    });

    it('should return false when no access token exists', () => {
      const result = service.isAuthenticated();

      expect(result).toBe(false);
    });
  });

  describe('refreshCompleteUserData', () => {
    it('should fetch and store complete user data', () => {
      service.refreshCompleteUserData().subscribe((user) => {
        expect(user).toEqual(mockCompleteUser);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/me`);
      expect(req.request.method).toBe('GET');
      req.flush(mockCompleteUser);

      // Check that data was stored in localStorage
      const storedCompleteUser = localStorage.getItem('complete_user');
      expect(storedCompleteUser).toBeTruthy();
      expect(JSON.parse(storedCompleteUser!)).toEqual(mockCompleteUser);

      // Check that basic user data was also updated
      const storedUser = localStorage.getItem('current_user');
      expect(storedUser).toBeTruthy();
    });

    it('should preserve existing JWT_UID when updating basic user data', () => {
      // Set existing user with JWT_UID
      service['currentUserSubject'].next(mockUser);

      service.refreshCompleteUserData().subscribe();

      const req = httpMock.expectOne(`${environment.apiUrl}/users/me`);
      req.flush(mockCompleteUser);

      const storedUser = localStorage.getItem('current_user');
      const parsedUser = JSON.parse(storedUser!);
      expect(parsedUser.JWT_UID).toBe('jwt123');
    });
  });

  describe('loadCompleteUserData', () => {
    it('should load complete user data when authenticated', () => {
      localStorage.setItem('access_token', 'mock-token');

      service.loadCompleteUserData().subscribe((user) => {
        expect(user).toEqual(mockCompleteUser);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/me`);
      req.flush(mockCompleteUser);
    });

    it('should throw error when not authenticated', () => {
      service.loadCompleteUserData().subscribe({
        error: (error) => {
          expect(error.message).toBe('User not authenticated');
        },
      });
    });
  });

  describe('login', () => {
    it('should login user and handle authentication', () => {
      const credentials: UserCredentials = {
        identifier: '<EMAIL>',
        password: 'password123',
      };

      service.login(credentials).subscribe((user) => {
        expect(user).toEqual(mockUser);
      });

      const loginReq = httpMock.expectOne(`${environment.apiUrl}/auth/login`);
      expect(loginReq.request.method).toBe('POST');
      expect(loginReq.request.body).toEqual(credentials);
      loginReq.flush(mockAuthResponse);

      // Should also try to load complete user data
      const completeUserReq = httpMock.expectOne(
        `${environment.apiUrl}/users/me`
      );
      completeUserReq.flush(mockCompleteUser);

      // Check that tokens were stored
      expect(localStorage.getItem('access_token')).toBe('mock-access-token');
      expect(localStorage.getItem('refresh_token')).toBe('mock-refresh-token');
      expect(localStorage.getItem('token_type')).toBe('Bearer');
    });
  });

  describe('register', () => {
    it('should register user and handle authentication', () => {
      const registrationData: RegistrationData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
        phone_number: '+1234567890',
      };

      service.register(registrationData).subscribe((user) => {
        expect(user).toEqual(mockUser);
      });

      const registerReq = httpMock.expectOne(
        `${environment.apiUrl}/users/register`
      );
      expect(registerReq.request.method).toBe('POST');
      expect(registerReq.request.body).toEqual(registrationData);
      registerReq.flush(mockAuthResponse);

      // Should also try to load complete user data
      const completeUserReq = httpMock.expectOne(
        `${environment.apiUrl}/users/me`
      );
      completeUserReq.flush(mockCompleteUser);
    });
  });

  describe('logout', () => {
    it('should clear all stored data and reset subjects', () => {
      // Set up some stored data
      localStorage.setItem('access_token', 'token');
      localStorage.setItem('refresh_token', 'refresh');
      localStorage.setItem('token_type', 'Bearer');
      localStorage.setItem('current_user', JSON.stringify(mockUser));
      localStorage.setItem('complete_user', JSON.stringify(mockCompleteUser));

      service['currentUserSubject'].next(mockUser);
      service['completeUserSubject'].next(mockCompleteUser);

      service.logout();

      // Check that localStorage was cleared
      expect(localStorage.getItem('access_token')).toBeNull();
      expect(localStorage.getItem('refresh_token')).toBeNull();
      expect(localStorage.getItem('token_type')).toBeNull();
      expect(localStorage.getItem('current_user')).toBeNull();
      expect(localStorage.getItem('complete_user')).toBeNull();

      // Check that subjects were reset
      expect(service.getCurrentUser()).toBeNull();
      expect(service.getCompleteUser()).toBeNull();
    });
  });

  describe('requestPasswordReset', () => {
    it('should request password reset', () => {
      const email = '<EMAIL>';
      const response = { message: 'Reset email sent' };

      service.requestPasswordReset(email).subscribe((result) => {
        expect(result).toEqual(response);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/auth/request-reset`
      );
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ email });
      req.flush(response);
    });
  });

  describe('resetPassword', () => {
    it('should reset password with token', () => {
      const token = 'reset-token';
      const newPassword = 'newpassword123';
      const response = { message: 'Password reset successful' };

      service.resetPassword(token, newPassword).subscribe((result) => {
        expect(result).toEqual(response);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/auth/reset-password`
      );
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ token, newPassword });
      req.flush(response);
    });
  });

  describe('Email OTP', () => {
    it('should send email OTP', () => {
      const email = '<EMAIL>';
      const response = { message: 'OTP sent' };

      service.sendEmailOtp(email).subscribe((result) => {
        expect(result).toEqual(response);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/oauth/email/send-otp`
      );
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ email });
      req.flush(response);
    });

    it('should verify email OTP', () => {
      const email = '<EMAIL>';
      const code = '123456';

      service.verifyEmailOtp(email, code).subscribe((result) => {
        expect(result).toEqual(mockAuthResponse);
      });

      const verifyReq = httpMock.expectOne(
        `${environment.apiUrl}/oauth/email/verify-otp`
      );
      expect(verifyReq.request.method).toBe('POST');
      expect(verifyReq.request.body).toEqual({ email, code });
      verifyReq.flush(mockAuthResponse);

      // Should also try to load complete user data
      const completeUserReq = httpMock.expectOne(
        `${environment.apiUrl}/users/me`
      );
      completeUserReq.flush(mockCompleteUser);
    });
  });

  describe('Phone OTP', () => {
    it('should send phone OTP', () => {
      const phoneNumber = '+1234567890';
      const recaptchaToken = 'recaptcha-token';
      const response = { sessionInfo: 'session-info' };

      service.sendPhoneOtp(phoneNumber, recaptchaToken).subscribe((result) => {
        expect(result).toEqual(response);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/oauth/phone/send-code`
      );
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ phoneNumber, recaptchaToken });
      req.flush(response);
    });

    it('should verify phone OTP', () => {
      const sessionInfo = 'session-info';
      const code = '123456';

      service.verifyPhoneOtp(sessionInfo, code).subscribe((result) => {
        expect(result).toEqual(mockAuthResponse);
      });

      const verifyReq = httpMock.expectOne(
        `${environment.apiUrl}/oauth/phone/verify-code`
      );
      expect(verifyReq.request.method).toBe('POST');
      expect(verifyReq.request.body).toEqual({ sessionInfo, code });
      verifyReq.flush(mockAuthResponse);

      // Should also try to load complete user data
      const completeUserReq = httpMock.expectOne(
        `${environment.apiUrl}/users/me`
      );
      completeUserReq.flush(mockCompleteUser);
    });
  });

  describe('Token methods', () => {
    beforeEach(() => {
      localStorage.setItem('access_token', 'access-token');
      localStorage.setItem('refresh_token', 'refresh-token');
      localStorage.setItem('token_type', 'Bearer');
    });

    it('should get access token', () => {
      expect(service.getAccessToken()).toBe('access-token');
    });

    it('should get refresh token', () => {
      expect(service.getRefreshToken()).toBe('refresh-token');
    });

    it('should get token type', () => {
      expect(service.getTokenType()).toBe('Bearer');
    });
  });

  describe('Storage methods', () => {
    it('should get user from storage', () => {
      localStorage.setItem('current_user', JSON.stringify(mockUser));

      const result = service['getUserFromStorage']();

      expect(result).toEqual(mockUser);
    });

    it('should return null when no user in storage', () => {
      const result = service['getUserFromStorage']();

      expect(result).toBeNull();
    });

    it('should get complete user from storage', () => {
      localStorage.setItem('complete_user', JSON.stringify(mockCompleteUser));

      const result = service['getCompleteUserFromStorage']();

      expect(result).toEqual(mockCompleteUser);
    });

    it('should return null when no complete user in storage', () => {
      const result = service['getCompleteUserFromStorage']();

      expect(result).toBeNull();
    });
  });
});
