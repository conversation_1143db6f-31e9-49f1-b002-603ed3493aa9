<div class="max-w-4xl mx-auto">
  <div class="text-center mb-8">
    <h2 class="text-2xl font-bold text-gray-900">Set Up Your Organization</h2>
    <p class="mt-2 text-gray-600">
      Step {{ currentStep }} of {{ totalSteps }}:
      {{
        currentStep === 1
          ? "Organization Details & Team Setup"
          : "Review & Complete"
      }}
    </p>
  </div>

  <!-- Progress Bar -->
  <div class="mb-8">
    <div class="flex items-center justify-center">
      <div class="flex items-center space-x-4">
        <div class="flex items-center">
          <div
            [class]="
              'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ' +
              (currentStep >= 1
                ? 'bg-indigo-600 text-white'
                : 'bg-gray-200 text-gray-600')
            "
          >
            1
          </div>
          <span class="ml-2 text-sm font-medium"
            >Organization & Team Setup</span
          >
        </div>
        <div
          [class]="
            'w-16 h-1 ' + (currentStep >= 2 ? 'bg-indigo-600' : 'bg-gray-200')
          "
        ></div>
        <div class="flex items-center">
          <div
            [class]="
              'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ' +
              (currentStep >= 2
                ? 'bg-indigo-600 text-white'
                : 'bg-gray-200 text-gray-600')
            "
          >
            2
          </div>
          <span class="ml-2 text-sm font-medium">Review & Complete</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Alert for errors -->
  <app-alert
    *ngIf="showAlert"
    [type]="alertType"
    [message]="alertMessage"
    [dismissible]="true"
  ></app-alert>

  <!-- Step 1: Organization Details -->
  <div *ngIf="currentStep === 1">
    <form [formGroup]="organizationForm" class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Organization Display Name -->
        <div>
          <label
            for="displayName"
            class="block text-sm font-medium text-gray-700"
          >
            Organization Display Name *
          </label>
          <input
            id="displayName"
            type="text"
            formControlName="displayName"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            placeholder="Your Organization Name"
          />
          <div
            *ngIf="
              organizationForm.get('displayName')?.invalid &&
              organizationForm.get('displayName')?.touched
            "
            class="mt-1 text-sm text-red-600"
          >
            Organization name is required
          </div>
        </div>

        <!-- Legal Name -->
        <div>
          <label
            for="legalName"
            class="block text-sm font-medium text-gray-700"
          >
            Legal Name (Optional)
          </label>
          <input
            id="legalName"
            type="text"
            formControlName="legalName"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            placeholder="Legal entity name"
          />
        </div>
      </div>

      <!-- Organization Subdomain -->
      <div>
        <label for="subdomain" class="block text-sm font-medium text-gray-700">
          Organization Subdomain *
        </label>
        <div class="mt-1 flex rounded-md shadow-sm">
          <input
            id="subdomain"
            type="text"
            formControlName="subdomain"
            (input)="onSubdomainChange()"
            class="flex-1 block w-full px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            placeholder="yourcompany"
          />
          <span
            class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm"
          >
            .digimeet.live
          </span>
        </div>
        <p class="mt-1 text-sm text-gray-500">
          Preview: <strong>{{ subdomainPreview }}</strong>
        </p>
        <div
          *ngIf="
            organizationForm.get('subdomain')?.invalid &&
            organizationForm.get('subdomain')?.touched
          "
          class="mt-1 text-sm text-red-600"
        >
          Subdomain is required and can only contain lowercase letters, numbers,
          and hyphens
        </div>
      </div>

      <!-- Organization Logo -->
      <div>
        <label for="logo" class="block text-sm font-medium text-gray-700">
          Organization Logo (Optional)
        </label>
        <div
          class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md"
        >
          <div class="space-y-1 text-center">
            <svg
              class="mx-auto h-12 w-12 text-gray-400"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <div class="flex text-sm text-gray-600">
              <label
                for="logo"
                class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
              >
                <span>Upload a file</span>
                <input
                  id="logo"
                  type="file"
                  class="sr-only"
                  accept="image/png,image/jpeg,image/gif"
                  (change)="onFileSelect($event)"
                />
              </label>
              <p class="pl-1">or drag and drop</p>
            </div>
            <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Industry -->
        <div>
          <label for="industry" class="block text-sm font-medium text-gray-700">
            Industry *
          </label>
          <select
            id="industry"
            formControlName="industry"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          >
            <option value="">Select industry</option>
            <option *ngFor="let industry of industries" [value]="industry">
              {{ industry }}
            </option>
          </select>
          <div
            *ngIf="
              organizationForm.get('industry')?.invalid &&
              organizationForm.get('industry')?.touched
            "
            class="mt-1 text-sm text-red-600"
          >
            Please select an industry
          </div>
        </div>

        <!-- Contact Email -->
        <div>
          <label
            for="contactEmail"
            class="block text-sm font-medium text-gray-700"
          >
            Contact Email *
          </label>
          <input
            id="contactEmail"
            type="email"
            formControlName="contactEmail"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            placeholder="<EMAIL>"
          />
          <div
            *ngIf="
              organizationForm.get('contactEmail')?.invalid &&
              organizationForm.get('contactEmail')?.touched
            "
            class="mt-1 text-sm text-red-600"
          >
            Please enter a valid contact email
          </div>
        </div>
      </div>

      <!-- Organization Roles -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">
          Organization Roles * (Select at least one)
        </label>
        <div class="space-y-4">
          <div *ngFor="let role of organizationRoles" class="flex items-start">
            <div class="flex items-center h-5">
              <input
                [id]="role.value"
                type="checkbox"
                [checked]="isRoleSelected(role.value)"
                (change)="onRoleChange(role.value, $any($event.target).checked)"
                class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
              />
            </div>
            <div class="ml-3">
              <label
                [for]="role.value"
                class="text-sm font-medium text-gray-900 cursor-pointer"
              >
                {{ role.label }}
              </label>
              <p class="text-sm text-gray-500">{{ role.description }}</p>
            </div>
          </div>
        </div>
        <div
          *ngIf="
            organizationForm.get('roles')?.invalid &&
            organizationForm.get('roles')?.touched
          "
          class="mt-1 text-sm text-red-600"
        >
          Please select at least one organization role
        </div>
      </div>

      <!-- Team Invitations Section -->
      <div class="border-t pt-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">
          Team Invitations (Optional)
        </h3>
        <p class="text-sm text-gray-600 mb-6">
          Invite team members to join your organization. They will receive email
          invitations once the organization is created.
        </p>

        <!-- Add Team Member Form -->
        <form
          [formGroup]="teamForm"
          (ngSubmit)="addTeamInvite()"
          class="bg-gray-50 p-6 rounded-lg mb-6"
        >
          <h4 class="text-md font-medium text-gray-900 mb-4">
            Add Team Member
          </h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="md:col-span-2">
              <label
                for="memberEmail"
                class="block text-sm font-medium text-gray-700"
              >
                Member Email
              </label>
              <input
                id="memberEmail"
                type="email"
                formControlName="memberEmail"
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label
                for="memberRole"
                class="block text-sm font-medium text-gray-700"
              >
                Role
              </label>
              <select
                id="memberRole"
                formControlName="memberRole"
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option
                  *ngFor="let role of availableRoles"
                  [value]="role.value"
                >
                  {{ role.label }}
                </option>
              </select>
            </div>
          </div>
          <div class="mt-4">
            <button
              type="submit"
              [disabled]="teamForm.invalid"
              class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Add Invite
            </button>
          </div>
        </form>

        <!-- Team Invites List -->
        <div *ngIf="teamInvites.length > 0" class="mb-6">
          <h4 class="text-md font-medium text-gray-900 mb-4">
            Pending Invitations
          </h4>
          <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
              <li
                *ngFor="let invite of teamInvites; let i = index"
                class="px-6 py-4"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <div
                        class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center"
                      >
                        <span class="text-sm font-medium text-gray-700">
                          {{ invite.email.charAt(0).toUpperCase() }}
                        </span>
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">
                        {{ invite.email }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ invite.role | titlecase }}
                      </div>
                    </div>
                  </div>
                  <div>
                    <button
                      type="button"
                      (click)="removeTeamInvite(i)"
                      class="text-red-600 hover:text-red-900 text-sm font-medium"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Navigation Buttons -->
      <div class="flex justify-end pt-6">
        <button
          type="button"
          (click)="nextStep()"
          [disabled]="loading"
          class="px-8 py-3 bg-indigo-600 text-white font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span
            *ngIf="loading"
            class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
          ></span>
          Create Organization
        </button>
      </div>
    </form>
  </div>

  <!-- Step 2: Review & Complete -->
  <div *ngIf="currentStep === 2">
    <div class="space-y-6">
      <div class="text-center">
        <div
          class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4"
        >
          <svg
            class="h-6 w-6 text-green-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900">
          Organization Created Successfully!
        </h3>
        <p class="mt-1 text-sm text-gray-600">
          Your organization has been created and is ready to use.
        </p>
      </div>

      <!-- Organization Summary -->
      <div class="bg-gray-50 p-6 rounded-lg">
        <h4 class="text-md font-medium text-gray-900 mb-4">
          Organization Summary
        </h4>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Organization Name:</span>
            <span class="text-sm font-medium text-gray-900">{{
              organizationForm.get("displayName")?.value
            }}</span>
          </div>
          <div
            class="flex justify-between"
            *ngIf="organizationForm.get('legalName')?.value"
          >
            <span class="text-sm text-gray-600">Legal Name:</span>
            <span class="text-sm font-medium text-gray-900">{{
              organizationForm.get("legalName")?.value
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Subdomain:</span>
            <span class="text-sm font-medium text-gray-900">{{
              subdomainPreview
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Contact Email:</span>
            <span class="text-sm font-medium text-gray-900">{{
              organizationForm.get("contactEmail")?.value
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Industry:</span>
            <span class="text-sm font-medium text-gray-900">{{
              organizationForm.get("industry")?.value
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Organization Roles:</span>
            <span class="text-sm font-medium text-gray-900">
              {{
                (organizationForm.get("roles")?.value || []).join(", ")
                  | titlecase
              }}
            </span>
          </div>
          <div class="flex justify-between" *ngIf="teamInvites.length > 0">
            <span class="text-sm text-gray-600">Team Invitations:</span>
            <span class="text-sm font-medium text-gray-900"
              >{{ teamInvites.length }} member(s) invited</span
            >
          </div>
        </div>
      </div>

      <!-- Team Invitations Summary -->
      <div *ngIf="teamInvites.length > 0" class="bg-blue-50 p-6 rounded-lg">
        <h4 class="text-md font-medium text-gray-900 mb-4">
          Team Invitations Sent
        </h4>
        <p class="text-sm text-gray-600 mb-4">
          The following team members have been invited and will receive email
          notifications:
        </p>
        <div class="space-y-2">
          <div
            *ngFor="let invite of teamInvites"
            class="flex justify-between items-center"
          >
            <span class="text-sm text-gray-900">{{ invite.email }}</span>
            <span
              class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full"
              >{{ invite.role | titlecase }}</span
            >
          </div>
        </div>
      </div>

      <!-- Navigation Buttons -->
      <div class="flex justify-between pt-6">
        <button
          type="button"
          (click)="previousStep()"
          class="px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Back to Edit
        </button>
        <button
          type="button"
          (click)="completeSetup()"
          [disabled]="loading"
          class="px-8 py-3 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span
            *ngIf="loading"
            class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
          ></span>
          Go to Dashboard
        </button>
      </div>
    </div>
  </div>
</div>
