import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, switchMap, catchError, tap } from 'rxjs/operators';
import { AuthService } from '../auth/auth.service';
import { UserService } from '../user/user.service';
import { SubdomainApiService } from '../subdomain-api/subdomain-api.service';
import {
  User,
  CompleteUser,
  UserProfileUpdateRequest,
  UserProfileUpdateResponse,
  DeleteUserResponse,
} from '../../models/user.model';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class UserDataService {
  private isLoadingSubject = new BehaviorSubject<boolean>(false);
  public isLoading$ = this.isLoadingSubject.asObservable();

  constructor(
    private authService: AuthService,
    private userService: UserService,
    private subdomainApiService: SubdomainApiService
  ) {}

  /**
   * Get current user (basic info)
   */
  getCurrentUser(): User | null {
    return this.authService.getCurrentUser();
  }

  /**
   * Get complete user profile with all extended information
   */
  getCompleteUser(): CompleteUser | null {
    return this.authService.getCompleteUser();
  }

  /**
   * Observable for current user changes
   */
  get currentUser$(): Observable<User | null> {
    return this.authService.currentUser$;
  }

  /**
   * Observable for complete user changes
   */
  get completeUser$(): Observable<CompleteUser | null> {
    return this.authService.completeUser$;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }

  /**
   * Refresh all user data from server
   */
  refreshUserData(): Observable<CompleteUser> {
    this.isLoadingSubject.next(true);
    return this.authService.refreshCompleteUserData().pipe(
      tap(() => this.isLoadingSubject.next(false)),
      catchError((error) => {
        this.isLoadingSubject.next(false);
        throw error;
      })
    );
  }

  /**
   * Load complete user data if authenticated
   */
  loadCompleteUserData(): Observable<CompleteUser | null> {
    if (!this.isAuthenticated()) {
      return of(null);
    }

    // Check if we're on a subdomain and use subdomain-aware loading
    if (this.isOnSubdomain()) {
      return this.loadCompleteUserDataForSubdomain();
    }

    this.isLoadingSubject.next(true);
    return this.authService.loadCompleteUserData().pipe(
      tap(() => this.isLoadingSubject.next(false)),
      catchError((error) => {
        this.isLoadingSubject.next(false);
        throw error;
      })
    );
  }

  /**
   * Load user data specifically for subdomain with retry logic
   */
  private loadCompleteUserDataForSubdomain(): Observable<CompleteUser | null> {
    console.log(
      'UserDataService: Loading user data for subdomain with enhanced error handling'
    );

    this.isLoadingSubject.next(true);

    return this.subdomainApiService.getUserData().pipe(
      tap((userData) => {
        console.log(
          'UserDataService: Successfully loaded user data on subdomain:',
          userData
        );
        // Store the user data in AuthService for consistency
        if (userData) {
          localStorage.setItem('complete_user', JSON.stringify(userData));
          this.authService['completeUserSubject'].next(userData);
        }
        this.isLoadingSubject.next(false);
      }),
      catchError((error) => {
        console.error(
          'UserDataService: Failed to load user data on subdomain:',
          error
        );
        this.isLoadingSubject.next(false);

        // If it's a subdomain CORS error, try to use cached data
        if (error.name === 'SubdomainCorsError') {
          const cachedUser = this.authService.getCompleteUser();
          if (cachedUser) {
            console.log(
              'UserDataService: Using cached user data due to subdomain CORS error'
            );
            return of(cachedUser);
          }

          // If no cached data, suggest page refresh
          console.log(
            'UserDataService: No cached data available, suggesting page refresh'
          );
          setTimeout(() => {
            if (
              confirm(
                'Unable to load user data. Would you like to refresh the page to try again?'
              )
            ) {
              window.location.reload();
            }
          }, 1000);
        }

        throw error;
      })
    );
  }

  /**
   * Check if we're currently on a subdomain
   */
  private isOnSubdomain(): boolean {
    const host = window.location.host;

    if (environment.production) {
      return (
        host.includes('.digimeet.live') &&
        !host.startsWith('www.') &&
        host !== 'digimeet.live'
      );
    } else {
      // For local development with subdomains
      return host.includes('.localhost');
    }
  }

  /**
   * Update current user profile with automatic data refresh
   */
  updateProfile(
    userData: UserProfileUpdateRequest
  ): Observable<UserProfileUpdateResponse> {
    console.log('UserDataService.updateProfile called with:', userData);
    this.isLoadingSubject.next(true);
    return this.userService.updateCurrentUser(userData).pipe(
      tap((response) => {
        console.log('UserService.updateCurrentUser response:', response);
      }),
      switchMap((response) => {
        console.log('Starting data refresh after successful update');
        // After successful update, refresh complete user data
        return this.refreshUserData().pipe(
          tap(() => console.log('Data refresh completed')),
          map(() => response)
        );
      }),
      tap(() => this.isLoadingSubject.next(false)),
      catchError((error) => {
        console.error('UserDataService.updateProfile error:', error);
        this.isLoadingSubject.next(false);
        throw error;
      })
    );
  }

  /**
   * Delete current user account with automatic logout
   */
  deleteAccount(): Observable<DeleteUserResponse> {
    this.isLoadingSubject.next(true);
    return this.userService.deleteCurrentUser().pipe(
      tap(() => {
        // After successful deletion, logout user
        this.authService.logout();
        this.isLoadingSubject.next(false);
      }),
      catchError((error) => {
        this.isLoadingSubject.next(false);
        throw error;
      })
    );
  }

  /**
   * Get UserService instance for direct API access
   * Use this when you need direct access to UserService methods
   */
  get userApi(): UserService {
    return this.userService;
  }

  /**
   * Get user permissions summary
   */
  getUserPermissions(): Observable<{
    isAdmin: boolean;
    isGodSuperUser: boolean;
    hasSystemPrivileges: boolean;
    organizationCount: number;
    roles: string[];
  } | null> {
    return this.completeUser$.pipe(
      map((completeUser) => {
        if (!completeUser) return null;

        const roles = completeUser.roles?.map((r) => r.role) || [];
        const isAdmin = roles.includes('ADMIN') || roles.includes('admin');

        return {
          isAdmin,
          isGodSuperUser:
            completeUser.privilegeSummary?.isGodSuperUser || false,
          hasSystemPrivileges:
            completeUser.privilegeSummary?.hasSystemPrivileges || false,
          organizationCount:
            completeUser.privilegeSummary?.organizationCount || 0,
          roles,
        };
      })
    );
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: string): Observable<boolean> {
    return this.getUserPermissions().pipe(
      map((permissions) => permissions?.roles.includes(role) || false)
    );
  }

  /**
   * Check if user is admin
   */
  isAdmin(): Observable<boolean> {
    return this.getUserPermissions().pipe(
      map((permissions) => permissions?.isAdmin || false)
    );
  }

  /**
   * Get user organizations
   */
  getUserOrganizations(): Observable<any[]> {
    return this.completeUser$.pipe(
      map((completeUser) => completeUser?.organizations || [])
    );
  }

  /**
   * Initialize user data on app start
   */
  initializeUserData(): Observable<CompleteUser | null> {
    if (!this.isAuthenticated()) {
      return of(null);
    }

    // Check if we already have complete user data in storage
    const existingCompleteUser = this.getCompleteUser();
    if (existingCompleteUser) {
      return of(existingCompleteUser);
    }

    // Load complete user data from server
    return this.loadCompleteUserData();
  }
}
