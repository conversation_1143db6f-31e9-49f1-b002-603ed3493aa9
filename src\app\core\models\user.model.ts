import { Role } from './role.enum';

export interface User {
  _id: string;
  JWT_UID: string;
  email: string;
  name: string;
  phone_number?: string;
  roles?: Role[];
  createdAt?: Date;
  updatedAt?: Date;
}

// Extended user profile for detailed user information
export interface UserProfile {
  fullName?: string;
  jobTitle?: string;
  companyName?: string;
  bio?: string;
  industryTags?: string[];
  networkingGoal?: string;
  delegateEmail?: string;
  avatar?: string;
  timezone?: string;
  language?: string;
  preferences?: any;
}

// Organization role information
export interface OrganizationRole {
  org: string;
  role: string;
  assignedAt: Date;
}

// System privilege information
export interface SystemPrivilege {
  level: string;
  grantedAt: Date;
  grantedBy: string;
}

// Organization information
export interface Organization {
  _id: string;
  name: string;
  subdomain: string;
  status: string;
  branding: {
    logoUrl?: string;
    primaryColor?: string;
  };
  id?: string; // Sometimes included in API responses
}

// Privilege summary
export interface PrivilegeSummary {
  isGodSuperUser: boolean;
  hasSystemPrivileges: boolean;
  highestPrivilegeLevel: number;
  organizationCount: number;
  hasDefaultOrganization: boolean;
}

// Complete user profile with all extended information
export interface CompleteUser {
  _id: string;
  name?: string;
  email: string;
  phone_number?: string;
  type: 'individual' | 'organization';
  status: string;
  profile: UserProfile;
  roles: OrganizationRole[];
  systemPrivileges: SystemPrivilege[];
  organizations: Organization[];
  defaultOrganization?: Organization;
  privilegeSummary: PrivilegeSummary;
}

// Paginated users response
export interface PaginatedUsersResponse {
  page: number;
  limit: number;
  total: number;
  users: {
    email: string;
    name: string;
    phone_number?: string;
  }[];
}

// User profile update request
export interface UserProfileUpdateRequest {
  name?: string;
  email?: string;
  phone_number?: string;
  type?: 'individual' | 'organization';
  fullName?: string;
  jobTitle?: string;
  companyName?: string;
  bio?: string;
  industryTags?: string[];
  networkingGoal?: string;
  delegateEmail?: string;
}

// User profile update response
export interface UserProfileUpdateResponse {
  message: string;
  user: CompleteUser;
}

// User status update request
export interface UserStatusUpdateRequest {
  identifier: string; // email or phone number
  status: string;
}

// User status response
export interface UserStatusResponse {
  _id: string;
  email: string;
  phone_number?: string;
  status: string;
}

// User status update response
export interface UserStatusUpdateResponse {
  message: string;
  user: UserStatusResponse;
}

// Delete user response
export interface DeleteUserResponse {
  message: string;
}

export interface UserCredentials {
  identifier: string; // email or phone number
  password: string;
}

export interface RegistrationData {
  email: string;
  password: string;
  name: string;
  phone_number?: string;
}

export interface AuthResponse {
  message: string;
  user: User;
  idToken: string;
  refreshToken: string;
  expiresIn: string;
  // Legacy fields for backward compatibility
  access_token?: string;
  refresh_token?: string;
  expires_in?: string;
  token_type?: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
}

export interface EmailOtpRequest {
  email: string;
}

export interface EmailOtpVerify {
  email: string;
  code: string;
}

export interface PhoneOtpRequest {
  phoneNumber: string;
  recaptchaToken: string;
}

export interface PhoneOtpVerify {
  sessionInfo: string;
  code: string;
}

export interface ApiError {
  error?: string;
  message?: string;
  details?: string;
}
