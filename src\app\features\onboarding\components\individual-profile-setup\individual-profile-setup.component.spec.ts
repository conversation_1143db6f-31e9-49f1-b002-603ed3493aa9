import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { UserDataService } from '../../../../core/services/user-data/user-data.service';
import { IndividualProfileSetupComponent } from './individual-profile-setup.component';

describe('IndividualProfileSetupComponent', () => {
  let component: IndividualProfileSetupComponent;
  let fixture: ComponentFixture<IndividualProfileSetupComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockUserDataService: jasmine.SpyObj<UserDataService>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const userDataServiceSpy = jasmine.createSpyObj('UserDataService', ['updateProfile']);

    await TestBed.configureTestingModule({
      declarations: [IndividualProfileSetupComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: UserDataService, useValue: userDataServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(IndividualProfileSetupComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockUserDataService = TestBed.inject(UserDataService) as jasmine.SpyObj<UserDataService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values', () => {
    expect(component.profileForm.get('fullName')?.value).toBe('');
    expect(component.profileForm.get('jobTitle')?.value).toBe('');
    expect(component.profileForm.get('companyName')?.value).toBe('');
  });

  it('should add tag to selectedTags when addTag is called', () => {
    component.addTag('Technology');
    expect(component.selectedTags).toContain('Technology');
  });

  it('should not add duplicate tags', () => {
    component.addTag('Technology');
    component.addTag('Technology');
    expect(component.selectedTags.length).toBe(1);
  });

  it('should not add more than 5 tags', () => {
    for (let i = 0; i < 6; i++) {
      component.addTag(`Tag${i}`);
    }
    expect(component.selectedTags.length).toBe(5);
  });

  it('should remove tag from selectedTags when removeTag is called', () => {
    component.addTag('Technology');
    component.removeTag('Technology');
    expect(component.selectedTags).not.toContain('Technology');
  });

  it('should return correct bio character count', () => {
    component.profileForm.patchValue({ bio: 'Test bio' });
    expect(component.bioCharacterCount).toBe(8);
  });

  it('should submit profile successfully', () => {
    mockUserDataService.updateProfile.and.returnValue(of({}));
    
    component.profileForm.patchValue({
      fullName: 'John Doe',
      jobTitle: 'Developer',
      companyName: 'Tech Corp',
      bio: 'Software developer',
      networkingGoal: 'Find new opportunities'
    });
    component.selectedTags = ['Technology'];

    component.onSubmit();

    expect(mockUserDataService.updateProfile).toHaveBeenCalled();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);
  });

  it('should handle profile update error', () => {
    mockUserDataService.updateProfile.and.returnValue(throwError({ message: 'Update failed' }));
    
    component.profileForm.patchValue({
      fullName: 'John Doe',
      jobTitle: 'Developer',
      companyName: 'Tech Corp',
      bio: 'Software developer',
      networkingGoal: 'Find new opportunities'
    });
    component.selectedTags = ['Technology'];

    component.onSubmit();

    expect(component.showAlert).toBe(true);
    expect(component.alertType).toBe('error');
  });
});
