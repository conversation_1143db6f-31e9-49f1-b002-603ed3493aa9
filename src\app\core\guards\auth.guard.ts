import { Injectable } from '@angular/core';
import {
  Router,
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth/auth.service';
import { UserDataService } from '../services/user-data/user-data.service';
import { SubdomainService } from '../services/subdomain/subdomain.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(
    private router: Router,
    private authService: AuthService,
    private userDataService: UserDataService,
    private subdomainService: SubdomainService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {
    const isAuthenticated = this.authService.isAuthenticated();
    const requestedUrl = state.url;

    console.log('AuthGuard: Checking access to:', requestedUrl, {
      isAuthenticated,
      route: route.routeConfig?.path,
    });

    if (!isAuthenticated) {
      // User is not logged in, redirect to login page with return URL
      console.log('AuthGuard: User not authenticated, redirecting to login');
      this.router.navigate(['/auth/login'], {
        queryParams: { returnUrl: requestedUrl },
      });
      return false;
    }

    // User is authenticated, check if they're accessing the correct dashboard

    // If on subdomain-login route, avoid API calls to prevent CORS issues during token transfer
    const currentPath = window.location.pathname;
    const currentSubdomain = this.subdomainService.getCurrentSubdomain();
    const isSubdomainLoginRoute =
      currentPath === '/auth/subdomain-login' ||
      currentPath.startsWith('/auth/subdomain-login?');

    console.log('AuthGuard: Route detection:', {
      currentPath,
      isSubdomainLoginRoute,
      currentSubdomain,
    });

    if (currentSubdomain && isSubdomainLoginRoute) {
      console.log(
        'AuthGuard: On subdomain-login route, avoiding API call to prevent CORS issues during token transfer'
      );
      const cachedUser = this.authService.getCompleteUser();
      if (cachedUser) {
        console.log('AuthGuard: Using cached user data for subdomain routing');
        return of(this.handleUserAccess(cachedUser, requestedUrl));
      }

      // No cached data available during token transfer - allow access
      // The subdomain-login component will handle the token transfer
      console.log(
        'AuthGuard: No cached data during token transfer, allowing access'
      );
      return of(true);
    }

    return this.userDataService.loadCompleteUserData().pipe(
      map((completeUser) => {
        return this.handleUserAccess(completeUser, requestedUrl);
      }),
      catchError((error) => {
        console.error('AuthGuard: Error loading user data:', error);

        // If this is a CORS error and we have cached user data, try to use it
        const cachedUser = this.authService.getCompleteUser();
        if (cachedUser && this.isCorsError(error)) {
          console.log(
            'AuthGuard: CORS error detected, using cached user data for routing'
          );
          return of(this.handleUserAccess(cachedUser, requestedUrl));
        }

        // On error without cached data, allow access but let other guards handle it
        return of(true);
      })
    );
  }

  private handleUserAccess(completeUser: any, requestedUrl: string): boolean {
    if (!completeUser) {
      console.log('AuthGuard: No user data found');
      return true; // Allow access, let other guards handle it
    }

    // Check onboarding completion first
    if (!this.authService.isOnboardingCompletedForUser(completeUser)) {
      if (requestedUrl.startsWith('/onboarding')) {
        console.log('AuthGuard: User accessing onboarding, allowing access');
        return true;
      } else {
        console.log(
          'AuthGuard: Onboarding not completed, redirecting to onboarding'
        );
        this.router.navigate(['/onboarding']);
        return false;
      }
    }

    // Onboarding is completed, check if user is accessing correct dashboard
    const userType = completeUser.type;
    const isIndividual = userType === 'individual';
    const isOrganization = userType === 'organization';

    console.log('AuthGuard: User type routing check:', {
      userType,
      requestedUrl,
      isIndividual,
      isOrganization,
    });

    // Individual users should only access /dashboard
    if (isIndividual) {
      if (
        requestedUrl.startsWith('/dashboard') &&
        !requestedUrl.startsWith('/org-dashboard')
      ) {
        console.log('AuthGuard: Individual user accessing correct dashboard');
        return true;
      } else if (requestedUrl.startsWith('/org-dashboard')) {
        console.log(
          'AuthGuard: Individual user trying to access org dashboard, redirecting'
        );
        this.router.navigate(['/dashboard']);
        return false;
      }
    }

    // Organization users should only access /org-dashboard
    if (isOrganization) {
      if (requestedUrl.startsWith('/org-dashboard')) {
        console.log('AuthGuard: Organization user accessing correct dashboard');
        return true;
      } else if (
        requestedUrl.startsWith('/dashboard') &&
        !requestedUrl.startsWith('/org-dashboard')
      ) {
        console.log(
          'AuthGuard: Organization user trying to access individual dashboard, redirecting'
        );
        this.router.navigate(['/org-dashboard']);
        return false;
      }
    }

    // For other routes (admin, etc.), allow access
    console.log('AuthGuard: Allowing access to other routes');
    return true;
  }

  private isCorsError(error: any): boolean {
    return (
      error &&
      (error.message?.includes('CORS') ||
        error.message?.includes('Access-Control-Allow-Origin') ||
        error.status === 0 ||
        (error.error instanceof ProgressEvent && error.status === 0))
    );
  }
}
