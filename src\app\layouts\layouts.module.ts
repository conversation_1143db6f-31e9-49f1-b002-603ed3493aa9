import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PublicLayoutComponent } from './public-layout/public-layout.component';
import { AdminLayoutComponent } from './admin-layout/admin-layout.component';
import { UserLayoutComponent } from './user-layout/user-layout.component';
import { OrganizationLayoutComponent } from './organization-layout/organization-layout.component';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../shared/shared.module';

@NgModule({
  declarations: [
    PublicLayoutComponent,
    AdminLayoutComponent,
    UserLayoutComponent,
    OrganizationLayoutComponent,
  ],
  imports: [CommonModule, RouterModule, SharedModule],
  exports: [
    PublicLayoutComponent,
    AdminLayoutComponent,
    UserLayoutComponent,
    OrganizationLayoutComponent,
    SharedModule,
  ],
})
export class LayoutsModule {}
