<div class="relative" *ngIf="userOrganizations.length > 1">
  <!-- Current Organization Display -->
  <button
    (click)="toggleDropdown()"
    class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
    [class.ring-2]="isDropdownOpen"
    [class.ring-blue-500]="isDropdownOpen"
  >
    <!-- Organization Icon -->
    <div
      class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center"
    >
      <span class="text-xs font-semibold text-blue-600">
        {{ getOrganizationInitials(currentOrganization) }}
      </span>
    </div>

    <!-- Organization Name -->
    <span class="max-w-32 truncate">{{
      currentOrganization?.name || "Select Organization"
    }}</span>

    <!-- Dropdown Arrow -->
    <svg
      class="w-4 h-4 transition-transform duration-200"
      [class.rotate-180]="isDropdownOpen"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M19 9l-7 7-7-7"
      ></path>
    </svg>
  </button>

  <!-- Dropdown Menu -->
  <div
    *ngIf="isDropdownOpen"
    class="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50"
    (clickOutside)="closeDropdown()"
  >
    <div class="py-1">
      <!-- Header -->
      <div
        class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100"
      >
        Switch Organization
      </div>

      <!-- Organization List -->
      <div class="max-h-60 overflow-y-auto">
        <button
          *ngFor="let org of userOrganizations; trackBy: trackByOrgId"
          (click)="switchToOrganization(org)"
          class="w-full px-4 py-3 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-150"
          [class.bg-blue-50]="isCurrentOrganization(org)"
          [class.border-l-4]="isCurrentOrganization(org)"
          [class.border-blue-500]="isCurrentOrganization(org)"
        >
          <div class="flex items-center space-x-3">
            <!-- Organization Avatar -->
            <div class="flex-shrink-0">
              <div
                *ngIf="org.branding?.logoUrl; else initialsAvatar"
                class="w-8 h-8 rounded-full overflow-hidden"
              >
                <img
                  [src]="org.branding.logoUrl"
                  [alt]="org.name"
                  class="w-full h-full object-cover"
                  (error)="onImageError($event, org)"
                />
              </div>
              <ng-template #initialsAvatar>
                <div
                  class="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold"
                  [style.background-color]="
                    org.branding?.primaryColor || '#3B82F6'
                  "
                >
                  {{ getOrganizationInitials(org) }}
                </div>
              </ng-template>
            </div>

            <!-- Organization Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2">
                <p class="text-sm font-medium text-gray-900 truncate">
                  {{ org.name }}
                </p>
                <span
                  *ngIf="isDefaultOrganization(org)"
                  class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                >
                  Default
                </span>
                <span
                  *ngIf="isCurrentOrganization(org)"
                  class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"
                >
                  Current
                </span>
              </div>
              <p class="text-xs text-gray-500 truncate">
                {{ org.subdomain }}.digimeet.live
              </p>
              <p class="text-xs text-gray-400 capitalize">
                Status: {{ org.status }}
              </p>
            </div>

            <!-- External Link Icon -->
            <div class="flex-shrink-0" *ngIf="!isCurrentOrganization(org)">
              <svg
                class="w-4 h-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                ></path>
              </svg>
            </div>
          </div>
        </button>
      </div>

      <!-- Footer -->
      <div class="border-t border-gray-100 px-4 py-2">
        <p class="text-xs text-gray-500">
          You have access to {{ userOrganizations.length }} organization{{
            userOrganizations.length !== 1 ? "s" : ""
          }}
        </p>
      </div>
    </div>
  </div>
</div>
