import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-loader',
  templateUrl: './loader.component.html',
  styleUrls: ['./loader.component.css'],
})
export class LoaderComponent {
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() color: 'primary' | 'white' = 'primary';
  @Input() fullScreen: boolean = false;
  @Input() overlay: boolean = false;
  @Input() message: string = '';

  get sizeClass(): string {
    switch (this.size) {
      case 'sm':
        return 'h-4 w-4';
      case 'lg':
        return 'h-12 w-12';
      default:
        return 'h-8 w-8';
    }
  }

  get colorClass(): string {
    return this.color === 'primary' ? 'text-indigo-600' : 'text-white';
  }
}
