import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth/auth.service';

@Component({
  selector: 'app-logout-complete',
  templateUrl: './logout-complete.component.html',
  styleUrls: ['./logout-complete.component.css'],
})
export class LogoutCompleteComponent implements OnInit {
  constructor(private authService: AuthService, private router: Router) {}

  ngOnInit(): void {
    console.log('LogoutCompleteComponent: Completing cross-domain logout');

    // Complete the logout process on main domain immediately
    this.completeLogoutProcess();
  }

  private completeLogoutProcess(): void {
    try {
      // Clear all authentication data from main domain
      this.authService.completeLogoutFromSubdomain();

      console.log(
        'LogoutCompleteComponent: Logout completed, redirecting to login'
      );

      // Use window.location.href to ensure a clean redirect without any guards
      const protocol = window.location.protocol;
      const host = window.location.host;
      window.location.href = `${protocol}//${host}/auth/login`;
    } catch (error) {
      console.error(
        'LogoutCompleteComponent: Error during logout completion:',
        error
      );

      // Fallback: force redirect to login even if there's an error
      window.location.href = `${window.location.protocol}//${window.location.host}/auth/login`;
    }
  }
}
