import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, throwError, timer } from 'rxjs';
import { catchError, retry, retryWhen, delayWhen, take, concatMap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class SubdomainApiService {
  private readonly maxRetries = 3;
  private readonly retryDelay = 1000; // 1 second
  private readonly maxRetryDelay = 5000; // 5 seconds

  constructor(private http: HttpClient) {}

  /**
   * Make an API call with subdomain-specific retry logic
   */
  makeApiCall<T>(endpoint: string, options: any = {}): Observable<T> {
    const url = `${environment.apiUrl}${endpoint}`;
    
    console.log('SubdomainApiService: Making API call to:', url);
    console.log('SubdomainApiService: Current host:', window.location.host);
    console.log('SubdomainApiService: Is on subdomain:', this.isOnSubdomain());

    return this.http.get<T>(url, options).pipe(
      retryWhen(errors =>
        errors.pipe(
          concatMap((error, index) => {
            const retryAttempt = index + 1;
            
            // Check if this is a CORS error
            if (this.isCorsError(error) && retryAttempt <= this.maxRetries) {
              const delay = Math.min(this.retryDelay * Math.pow(2, index), this.maxRetryDelay);
              
              console.log(`SubdomainApiService: CORS error detected, retrying in ${delay}ms (attempt ${retryAttempt}/${this.maxRetries})`);
              console.log('SubdomainApiService: Error details:', {
                status: error.status,
                statusText: error.statusText,
                message: error.message,
                url: error.url
              });
              
              return timer(delay);
            }
            
            // If not a CORS error or max retries reached, throw the error
            console.error(`SubdomainApiService: Non-CORS error or max retries reached:`, error);
            return throwError(() => error);
          }),
          take(this.maxRetries)
        )
      ),
      catchError((error) => {
        console.error('SubdomainApiService: Final error after all retries:', error);
        
        // If this is a CORS error on subdomain, provide helpful error message
        if (this.isCorsError(error) && this.isOnSubdomain()) {
          console.error('SubdomainApiService: CORS error on subdomain - API server may not be configured for subdomain access');
          
          // Return a more user-friendly error
          const corsError = new Error('Unable to connect to server from subdomain. Please try refreshing the page.');
          corsError.name = 'SubdomainCorsError';
          return throwError(() => corsError);
        }
        
        return throwError(() => error);
      })
    );
  }

  /**
   * Get user data with subdomain-specific handling
   */
  getUserData(): Observable<any> {
    return this.makeApiCall('/users/me');
  }

  /**
   * Check if current error is a CORS error
   */
  private isCorsError(error: any): boolean {
    return (
      error &&
      (error.status === 0 ||
        error.message?.includes('CORS') ||
        error.message?.includes('Access-Control-Allow-Origin') ||
        (error.error instanceof ProgressEvent && error.status === 0))
    );
  }

  /**
   * Check if we're currently on a subdomain
   */
  private isOnSubdomain(): boolean {
    const host = window.location.host;
    
    if (environment.production) {
      return (
        host.includes('.digimeet.live') &&
        !host.startsWith('www.') &&
        host !== 'digimeet.live'
      );
    } else {
      // For local development with subdomains
      return host.includes('.localhost');
    }
  }

  /**
   * Force a page refresh to help with CORS issues
   */
  forceRefreshForCors(): void {
    console.log('SubdomainApiService: Forcing page refresh to resolve CORS issues');
    
    // Add a small delay to allow any pending operations to complete
    setTimeout(() => {
      window.location.reload();
    }, 500);
  }

  /**
   * Check if the API server is reachable from current subdomain
   */
  testApiConnection(): Observable<boolean> {
    console.log('SubdomainApiService: Testing API connection from subdomain');
    
    return this.http.get(`${environment.apiUrl}/health`, { 
      headers: { 'Accept': 'application/json' }
    }).pipe(
      catchError((error) => {
        console.error('SubdomainApiService: API connection test failed:', error);
        return of(false);
      }),
      concatMap((response) => {
        console.log('SubdomainApiService: API connection test successful:', response);
        return of(true);
      })
    );
  }
}
