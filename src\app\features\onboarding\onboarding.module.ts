import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';

import { AccountTypeSelectionComponent } from './components/account-type-selection/account-type-selection.component';
import { IndividualProfileSetupComponent } from './components/individual-profile-setup/individual-profile-setup.component';
import { OrganizationProfileSetupComponent } from './components/organization-profile-setup/organization-profile-setup.component';
import { SharedModule } from '../../shared/shared.module';
import { OnboardingComponent } from './onboarding.component';

const routes: Routes = [
  {
    path: '',
    component: OnboardingComponent,
    children: [
      { path: '', redirectTo: 'account-type', pathMatch: 'full' },
      { path: 'account-type', component: AccountTypeSelectionComponent },
      {
        path: 'individual-profile',
        component: IndividualProfileSetupComponent,
      },
      {
        path: 'organization-profile',
        component: OrganizationProfileSetupComponent,
      },
    ],
  },
];

@NgModule({
  declarations: [
    OnboardingComponent,
    AccountTypeSelectionComponent,
    IndividualProfileSetupComponent,
    OrganizationProfileSetupComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    SharedModule,
  ],
})
export class OnboardingModule {}
