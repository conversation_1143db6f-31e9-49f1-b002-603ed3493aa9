<div class="flex h-screen">
  <!-- Sidebar Navigation -->
  <aside class="w-64 bg-gray-800 text-white">
    <div class="p-4">
      <h2 class="text-xl font-semibold">Dashboard</h2>
    </div>

    <nav class="mt-6">
      <ul>
        <li class="px-4 py-2 hover:bg-gray-700">
          <a
            routerLink="/dashboard"
            routerLinkActive="font-bold"
            [routerLinkActiveOptions]="{ exact: true }"
            >Overview</a
          >
        </li>
        <li class="px-4 py-2 hover:bg-gray-700">
          <a routerLink="/dashboard/profile" routerLinkActive="font-bold"
            >Profile</a
          >
        </li>
        <li class="px-4 py-2 hover:bg-gray-700">
          <a routerLink="/dashboard/settings" routerLinkActive="font-bold"
            >Settings</a
          >
        </li>
      </ul>
    </nav>

    <div class="mt-auto p-4">
      <button
        (click)="logout()"
        class="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded"
      >
        Logout
      </button>
    </div>
  </aside>

  <!-- Main Content -->
  <div class="flex-1 flex flex-col overflow-hidden">
    <!-- Header -->
    <header class="bg-white shadow">
      <div class="flex justify-between items-center px-6 py-4">
        <h1 class="text-xl font-semibold">Welcome, {{ userName }}</h1>
        <div>
          <!-- User dropdown or notifications could go here -->
        </div>
      </div>
    </header>

    <!-- Page Content -->
    <main class="flex-1 overflow-auto p-6 bg-gray-100">
      <router-outlet></router-outlet>
    </main>
  </div>
</div>
