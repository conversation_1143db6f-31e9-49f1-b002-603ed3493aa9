import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { ApiService } from '../api/api.service';
import {
  User,
  CompleteUser,
  PaginatedUsersResponse,
  UserProfileUpdateRequest,
  UserProfileUpdateResponse,
  UserStatusUpdateRequest,
  UserStatusUpdateResponse,
  UserStatusResponse,
  DeleteUserResponse,
} from '../../models/user.model';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private readonly path = '/users';

  constructor(private apiService: ApiService) {}

  /**
   * Get all users with pagination (RBAC)
   * @param page Page number (default: 1)
   * @param limit Number of users per page (default: 10)
   */
  getAllUsers(
    page: number = 1,
    limit: number = 10
  ): Observable<PaginatedUsersResponse> {
    const params = { page, limit };
    return this.apiService.get<PaginatedUsersResponse>(this.path, params);
  }

  /**
   * Get all users (admin only) - Legacy method
   */
  getUsers(params: any = {}): Observable<User[]> {
    return this.apiService.get<User[]>(this.path, params);
  }

  /**
   * Get user by ID
   * @param id User ID
   */
  getUserById(id: string): Observable<User> {
    return this.apiService.get<User>(`${this.path}/${id}`);
  }

  /**
   * Get current user profile - Legacy method
   */
  getCurrentUserProfile(): Observable<User> {
    return this.apiService.get<User>(`${this.path}/me`);
  }

  /**
   * Get the current authenticated user's complete profile
   * Including organizations, roles, and extended profile fields
   */
  getCurrentUserComplete(): Observable<CompleteUser> {
    return this.apiService.get<CompleteUser>(`${this.path}/me`);
  }

  /**
   * Update user
   * @param id User ID
   * @param userData Updated user data
   */
  updateUser(id: string, userData: Partial<User>): Observable<User> {
    return this.apiService.put<User>(`${this.path}/${id}`, userData);
  }

  /**
   * Update current user profile - Legacy method
   * @param userData Updated user data
   */
  updateProfile(userData: Partial<User>): Observable<User> {
    return this.apiService.put<User>(`${this.path}/me`, userData);
  }

  /**
   * Update the current user's profile information
   * @param userData Updated user profile data
   */
  updateCurrentUser(
    userData: UserProfileUpdateRequest
  ): Observable<UserProfileUpdateResponse> {
    console.log('UserService.updateCurrentUser called');
    console.log('API endpoint:', `${this.path}/update_user`);
    console.log('Request payload:', userData);

    return this.apiService
      .put<UserProfileUpdateResponse>(`${this.path}/update_user`, userData)
      .pipe(
        tap((response) => {
          console.log(
            'UserService.updateCurrentUser success response:',
            response
          );
        }),
        catchError((error) => {
          console.error('UserService.updateCurrentUser error:', error);
          throw error;
        })
      );
  }

  /**
   * Delete user (admin only)
   * @param id User ID
   */
  deleteUser(id: string): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  /**
   * Delete the current user (MongoDB + Firebase + remove from all orgs)
   */
  deleteCurrentUser(): Observable<DeleteUserResponse> {
    return this.apiService.delete<DeleteUserResponse>(
      `${this.path}/delete_user`
    );
  }

  /**
   * Update a user's status by email or phone number (RBAC)
   * @param statusData User status update data
   */
  updateUserStatus(
    statusData: UserStatusUpdateRequest
  ): Observable<UserStatusUpdateResponse> {
    return this.apiService.put<UserStatusUpdateResponse>(
      `${this.path}/status`,
      statusData
    );
  }

  /**
   * Get a user's status by email or phone number (RBAC)
   * @param identifier User's email or phone number
   */
  getUserStatus(identifier: string): Observable<UserStatusResponse> {
    const params = { identifier };
    return this.apiService.get<UserStatusResponse>(
      `${this.path}/status`,
      params
    );
  }
}
