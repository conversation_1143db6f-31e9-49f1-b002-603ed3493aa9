<div
	class="mx-auto font-[Inter] flex flex-col border border-gray-300 shadow-lg rounded-[50px] bg-white w-[524px] h-[640px]">

	<!-- Radial Gradient -->
	<div class="flex justify-center items-center w-full h-[126px] rounded-t-[50px] bg-radial-custom">
		<p class="text-white text-[27px] text-center w-[20vw]">Create Your Free Media
			Account Here </p>
	</div>


	<form [formGroup]="contactForm">
		<div class="p-6">
			<div class="form-group space-y-2">
				<div class="form-field flex flex-col">
					<label class="form-label text-lg mb-1">First Name *</label>
					<input type="text" formControlName="firstName"
						class="input w-full py-3 px-4 text-base max-w-[600px]" required />
				</div>

				<div class="form-field flex flex-col">
					<label class="form-label text-lg mb-1">Last Name *</label>
					<input type="text" formControlName="lastName"
						class="input w-full py-3 px-4 text-base max-w-[600px]" />
				</div>

				<div class="form-field flex flex-col">
					<label class="form-label text-lg mb-1">Email *</label>
					<input type="email" formControlName="email"
						class="input w-full py-3 px-4 text-base max-w-[600px]" />
				</div>

				<div class="form-field flex flex-col">
					<label class="form-label text-lg mb-1">Signup Code?</label>
					<input type="password" formControlName="signup"
						class="input w-full py-3 px-4 text-base max-w-[600px]" />
				</div>

				<!-- Button -->
				<div class="flex justify-center">
					<app-button (onBtnClick)="onSubmit()" btnText="Continue" btnColor="gradient"
						[isRounded]="true"></app-button>
				</div>
			</div>
		</div>
	</form>

</div>