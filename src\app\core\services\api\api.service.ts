import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  constructor(private http: HttpClient) {}

  /**
   * Perform a GET request
   * @param path API endpoint path
   * @param params Optional HTTP query parameters
   */
  get<T>(path: string, params: any = {}): Observable<T> {
    const httpParams = this.buildParams(params);
    return this.http
      .get<T>(`${environment.apiUrl}${path}`, { params: httpParams })
      .pipe(catchError((error) => this.handleError(error)));
  }

  /**
   * Perform a POST request
   * @param path API endpoint path
   * @param body Request body
   */
  post<T>(path: string, body: any = {}): Observable<T> {
    return this.http
      .post<T>(`${environment.apiUrl}${path}`, body)
      .pipe(catchError((error) => this.handleError(error)));
  }

  /**
   * Perform a PUT request
   * @param path API endpoint path
   * @param body Request body
   */
  put<T>(path: string, body: any = {}): Observable<T> {
    return this.http
      .put<T>(`${environment.apiUrl}${path}`, body)
      .pipe(catchError((error) => this.handleError(error)));
  }

  /**
   * Perform a PATCH request
   * @param path API endpoint path
   * @param body Request body
   */
  patch<T>(path: string, body: any = {}): Observable<T> {
    return this.http
      .patch<T>(`${environment.apiUrl}${path}`, body)
      .pipe(catchError((error) => this.handleError(error)));
  }

  /**
   * Perform a DELETE request
   * @param path API endpoint path
   */
  delete<T>(path: string): Observable<T> {
    return this.http
      .delete<T>(`${environment.apiUrl}${path}`)
      .pipe(catchError((error) => this.handleError(error)));
  }

  /**
   * Build HTTP params from object
   * @param params Parameters object
   */
  private buildParams(params: any): HttpParams {
    let httpParams = new HttpParams();

    Object.keys(params).forEach((key) => {
      if (params[key] !== undefined && params[key] !== null) {
        httpParams = httpParams.set(key, params[key].toString());
      }
    });

    return httpParams;
  }

  /**
   * Error handler
   * @param error HTTP error
   */
  private handleError(error: any): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = error.error?.message || error.statusText;
    }

    return throwError(() => new Error(errorMessage));
  }
}
