import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AccountTypeSelectionComponent } from './account-type-selection.component';

describe('AccountTypeSelectionComponent', () => {
  let component: AccountTypeSelectionComponent;
  let fixture: ComponentFixture<AccountTypeSelectionComponent>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      declarations: [AccountTypeSelectionComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AccountTypeSelectionComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty account type', () => {
    expect(component.accountTypeForm.get('accountType')?.value).toBe('');
  });

  it('should select account type when selectAccountType is called', () => {
    component.selectAccountType('individual');
    expect(component.accountTypeForm.get('accountType')?.value).toBe('individual');
  });

  it('should return true for isSelected when account type matches', () => {
    component.selectAccountType('organization');
    expect(component.isSelected('organization')).toBe(true);
    expect(component.isSelected('individual')).toBe(false);
  });

  it('should navigate to individual profile when individual is selected', () => {
    component.selectAccountType('individual');
    component.onContinue();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/onboarding/individual-profile']);
  });

  it('should navigate to organization profile when organization is selected', () => {
    component.selectAccountType('organization');
    component.onContinue();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/onboarding/organization-profile']);
  });

  it('should not navigate when no account type is selected', () => {
    component.onContinue();
    expect(mockRouter.navigate).not.toHaveBeenCalled();
  });
});
