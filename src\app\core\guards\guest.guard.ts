import { Injectable } from '@angular/core';
import { Router, CanActivate, ActivatedRouteSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth/auth.service';
import { UserDataService } from '../services/user-data/user-data.service';
import { SubdomainService } from '../services/subdomain/subdomain.service';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class GuestGuard implements CanActivate {
  constructor(
    private router: Router,
    private authService: AuthService,
    private userDataService: UserDataService,
    private subdomainService: SubdomainService
  ) {}

  canActivate(route?: ActivatedRouteSnapshot): Observable<boolean> | boolean {
    // Check if this is the logout-complete route
    const url = route?.url?.join('/') || '';
    const isLogoutComplete =
      url.includes('logout-complete') ||
      window.location.pathname.includes('logout-complete');

    if (isLogoutComplete) {
      console.log(
        'GuestGuard: Logout-complete route detected, allowing access'
      );
      return true;
    }

    const isAuthenticated = this.authService.isAuthenticated();

    console.log('GuestGuard: Checking if user should access auth pages:', {
      isAuthenticated,
      shouldAllowAccess: !isAuthenticated,
      url,
    });

    if (!isAuthenticated) {
      // User is not logged in, allow access to auth pages
      console.log(
        'GuestGuard: User not authenticated, allowing access to auth pages'
      );
      return true;
    }

    // User is authenticated, determine where to redirect them
    console.log(
      'GuestGuard: User is authenticated, determining redirect destination'
    );

    // If on subdomain-login route, avoid API calls to prevent CORS issues during token transfer
    const currentPath = window.location.pathname;
    const currentSubdomain = this.subdomainService.getCurrentSubdomain();
    const isSubdomainLoginRoute = currentPath.includes('/auth/subdomain-login');

    if (currentSubdomain && isSubdomainLoginRoute) {
      console.log(
        'GuestGuard: On subdomain-login route, avoiding API call to prevent CORS issues during token transfer'
      );
      const cachedUser = this.authService.getCompleteUser();
      if (cachedUser) {
        console.log('GuestGuard: Using cached user data for subdomain routing');
        return of(this.handleUserRedirect(cachedUser));
      }

      // No cached data available during token transfer - redirect directly to org-dashboard
      // The subdomain-login component will handle the token transfer and redirect
      console.log(
        'GuestGuard: No cached data during token transfer, redirecting directly to org-dashboard'
      );
      this.router.navigate(['/org-dashboard']);
      return of(false);
    }

    return this.userDataService.loadCompleteUserData().pipe(
      map((completeUser) => {
        return this.handleUserRedirect(completeUser);
      }),
      catchError((error) => {
        console.error('GuestGuard: Error loading user data:', error);

        // If this is a CORS error and we have cached user data, try to use it
        const cachedUser = this.authService.getCompleteUser();
        if (cachedUser && this.isCorsError(error)) {
          console.log(
            'GuestGuard: CORS error detected, using cached user data for routing'
          );
          return of(this.handleUserRedirect(cachedUser));
        }

        // Fallback to dashboard on error without cached data
        this.router.navigate(['/dashboard']);
        return of(false);
      })
    );
  }

  private handleUserRedirect(completeUser: any): boolean {
    if (!completeUser) {
      console.log('GuestGuard: No user data, redirecting to dashboard');
      this.router.navigate(['/dashboard']);
      return false;
    }

    // Check onboarding completion
    if (!this.authService.isOnboardingCompletedForUser(completeUser)) {
      console.log(
        'GuestGuard: Onboarding not completed, redirecting to onboarding'
      );
      this.router.navigate(['/onboarding']);
      return false;
    }

    // Route based on user type
    if (completeUser.type === 'individual') {
      console.log('GuestGuard: Individual user, redirecting to dashboard');
      this.router.navigate(['/dashboard']);
    } else if (completeUser.type === 'organization') {
      console.log(
        'GuestGuard: Organization user, redirecting to org-dashboard'
      );
      this.redirectToOrganizationDashboard(completeUser);
    } else {
      console.log('GuestGuard: Unknown user type, redirecting to dashboard');
      this.router.navigate(['/dashboard']);
    }

    return false; // Block access to auth pages
  }

  private redirectToOrganizationDashboard(completeUser: any) {
    // Prioritize default organization, then fall back to first organization
    let targetOrg = completeUser.defaultOrganization;

    if (
      !targetOrg &&
      completeUser.organizations &&
      completeUser.organizations.length > 0
    ) {
      targetOrg = completeUser.organizations[0];
    }

    if (
      targetOrg &&
      targetOrg.subdomain &&
      this.subdomainService.areSubdomainsEnabled()
    ) {
      // Check if already on correct subdomain
      if (!this.subdomainService.isOnCorrectSubdomain(targetOrg.subdomain)) {
        console.log(
          'GuestGuard: Redirecting to organization subdomain:',
          targetOrg.subdomain,
          '(from',
          completeUser.defaultOrganization ? 'default org' : 'first org',
          ')'
        );
        this.subdomainService.redirectToSubdomain(
          targetOrg.subdomain,
          '/org-dashboard'
        );
        return;
      }
    }

    // Fallback to regular org-dashboard navigation
    this.router.navigate(['/org-dashboard']);
  }

  private isCorsError(error: any): boolean {
    return (
      error &&
      (error.message?.includes('CORS') ||
        error.message?.includes('Access-Control-Allow-Origin') ||
        error.status === 0 ||
        (error.error instanceof ProgressEvent && error.status === 0))
    );
  }
}
