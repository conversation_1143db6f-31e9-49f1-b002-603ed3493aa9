.organization-management-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.management-content.loading {
  opacity: 0.6;
  pointer-events: none;
}

.current-org-section,
.user-orgs-section,
.create-org-section,
.update-org-section,
.member-management-section,
.all-orgs-section,
.permissions-info {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.current-org-section h2,
.user-orgs-section h2,
.create-org-section h2,
.update-org-section h2,
.member-management-section h2,
.all-orgs-section h2,
.permissions-info h3 {
  margin: 0 0 20px 0;
  color: #333;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 8px;
}

.current-org-card {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  background-color: #f8f9fa;
}

.org-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.org-info h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.subdomain {
  color: #666;
  font-size: 14px;
  margin: 0 0 8px 0;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status.active {
  background-color: #d4edda;
  color: #155724;
}

.status.pending {
  background-color: #fff3cd;
  color: #856404;
}

.status.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.org-branding {
  display: flex;
  align-items: center;
  gap: 12px;
}

.org-logo {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  object-fit: cover;
}

.color-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.org-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.no-org-selected,
.no-organizations {
  text-align: center;
  padding: 40px;
  color: #666;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.organizations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.org-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.org-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.org-card.selected {
  border-color: #007bff;
  background-color: #e7f3ff;
}

.org-card h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.org-card p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.org-card .branding {
  position: absolute;
  top: 16px;
  right: 16px;
}

.admin-view {
  border-left: 4px solid #dc3545;
}

.admin-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.create-org-form,
.update-org-form {
  max-width: 600px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #555;
  margin-bottom: 4px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group small {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.form-row {
  display: flex;
  gap: 16px;
  align-items: end;
  flex-wrap: wrap;
}

.form-row .form-group {
  flex: 1;
  min-width: 200px;
}

.add-user-form {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  background-color: #f8f9fa;
}

.add-user-form h3 {
  margin: 0 0 16px 0;
  color: #333;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  font-size: 14px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #1e7e34;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-info {
  background-color: #17a2b8;
  color: white;
}

.btn-info:hover:not(:disabled) {
  background-color: #138496;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.permission-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  background-color: #e9ecef;
  color: #495057;
  transition: all 0.2s ease;
}

.badge.active {
  background-color: #007bff;
  color: white;
}

@media (max-width: 768px) {
  .organization-management-container {
    padding: 10px;
  }
  
  .organizations-grid {
    grid-template-columns: 1fr;
  }
  
  .org-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .org-branding {
    margin-top: 12px;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .form-row .form-group {
    min-width: auto;
  }
}
