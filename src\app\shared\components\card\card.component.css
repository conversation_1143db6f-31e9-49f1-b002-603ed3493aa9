.card {
  border-radius: 2rem;
  box-shadow: 0px 0px 30px 0px #0C023E26;
  overflow: hidden;
  text-align: center;
  background-color: #e4ebfd;
}

.card:hover {
    background-color: white; 
  }

.card:hover .card-img-container {
    background: radial-gradient(50% 50% at 100% 0%, #F42447 59.03%, #F5327D 100%);

}

.card-img {
  width: auto;
  height: auto;
}

.card-content {
  padding: 10px;
}

.card-content h3 {
  margin: 20px 0;
  font-size: 1.625em;
}

.card-content p {
  color: #212121;
  font-size: 1.125rem;
  width: 15rem;
  margin: 20px 0;
}