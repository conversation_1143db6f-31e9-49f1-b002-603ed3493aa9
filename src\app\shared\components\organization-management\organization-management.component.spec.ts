import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { of, throwError } from 'rxjs';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { OrganizationManagementComponent } from './organization-management.component';
import { OrganizationDataService } from '../../../core/services/organization-data/organization-data.service';
import { Organization, CreateOrganizationRequest, PaginatedOrganizationsResponse } from '../../../core/models/organization.model';

describe('OrganizationManagementComponent', () => {
  let component: OrganizationManagementComponent;
  let fixture: ComponentFixture<OrganizationManagementComponent>;
  let organizationDataServiceSpy: jasmine.SpyObj<OrganizationDataService>;

  const mockOrganization: Organization = {
    _id: '663041cf7a14c7c000a3f999',
    name: 'Acme Corporation',
    subdomain: 'acme-corp',
    status: 'active',
    branding: {
      logoUrl: 'https://acme.com/logo.png',
      primaryColor: '#336699'
    },
    createdAt: new Date('2025-06-02T17:35:17.117Z'),
    updatedAt: new Date('2025-06-02T17:35:17.117Z')
  };

  const mockOrganizationApi = {
    getAllOrganizations: jasmine.createSpy('getAllOrganizations').and.returnValue(of({
      page: 1,
      limit: 50,
      total: 1,
      organizations: [mockOrganization]
    } as PaginatedOrganizationsResponse)),
    addUserToOrganization: jasmine.createSpy('addUserToOrganization').and.returnValue(of({
      success: true,
      message: 'User added successfully'
    })),
    removeUserFromOrganization: jasmine.createSpy('removeUserFromOrganization').and.returnValue(of({
      success: true,
      message: 'User removed successfully'
    }))
  };

  beforeEach(async () => {
    const orgDataSpy = jasmine.createSpyObj('OrganizationDataService', [
      'initializeOrganizationData',
      'createOrganizationByUser',
      'updateOrganization',
      'deleteOrganization',
      'loadOrganizationDetails',
      'setCurrentOrganization',
      'refreshCurrentOrganization',
      'isOrganizationAdmin',
      'isGodSuperUser',
      'canManageOrganization',
      'getUserOrganizations'
    ], {
      currentOrganization$: of(mockOrganization),
      isLoading$: of(false),
      organizationApi: mockOrganizationApi
    });

    await TestBed.configureTestingModule({
      declarations: [OrganizationManagementComponent],
      imports: [FormsModule],
      providers: [
        { provide: OrganizationDataService, useValue: orgDataSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(OrganizationManagementComponent);
    component = fixture.componentInstance;
    organizationDataServiceSpy = TestBed.inject(OrganizationDataService) as jasmine.SpyObj<OrganizationDataService>;

    // Setup default spy returns
    organizationDataServiceSpy.isOrganizationAdmin.and.returnValue(of(true));
    organizationDataServiceSpy.isGodSuperUser.and.returnValue(of(false));
    organizationDataServiceSpy.canManageOrganization.and.returnValue(of(true));
    organizationDataServiceSpy.getUserOrganizations.and.returnValue(of([mockOrganization]));
    organizationDataServiceSpy.initializeOrganizationData.and.returnValue(of(mockOrganization));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with organization data on ngOnInit', () => {
    fixture.detectChanges();

    expect(component.currentOrganization).toEqual(mockOrganization);
    expect(component.isAdmin).toBe(true);
    expect(component.isGodSuperUser).toBe(false);
    expect(component.canManage).toBe(true);
    expect(organizationDataServiceSpy.initializeOrganizationData).toHaveBeenCalled();
  });

  it('should display current organization information', () => {
    fixture.detectChanges();

    const orgNameElement = fixture.debugElement.query(By.css('.org-info h3'));
    const subdomainElement = fixture.debugElement.query(By.css('.subdomain'));
    const statusElement = fixture.debugElement.query(By.css('.status'));

    expect(orgNameElement.nativeElement.textContent.trim()).toBe('Acme Corporation');
    expect(subdomainElement.nativeElement.textContent.trim()).toBe('acme-corp');
    expect(statusElement.nativeElement.textContent.trim()).toBe('Active');
  });

  it('should display user organizations grid', () => {
    fixture.detectChanges();

    const orgCards = fixture.debugElement.queryAll(By.css('.user-orgs-section .org-card'));
    expect(orgCards.length).toBe(1);
    expect(orgCards[0].nativeElement.textContent).toContain('Acme Corporation');
  });

  it('should create organization when form is submitted', () => {
    const createResponse = {
      success: true,
      message: 'Organization created successfully',
      organization: mockOrganization
    };
    organizationDataServiceSpy.createOrganizationByUser.and.returnValue(of(createResponse));

    component.createOrgForm = {
      name: 'New Org',
      subdomain: 'new-org',
      branding: { logoUrl: '', primaryColor: '#336699' }
    };

    component.createOrganization();

    expect(organizationDataServiceSpy.createOrganizationByUser).toHaveBeenCalledWith(component.createOrgForm);
  });

  it('should show alert when creating organization without required fields', () => {
    spyOn(window, 'alert');
    component.createOrgForm = { name: '', subdomain: '', branding: {} };

    component.createOrganization();

    expect(window.alert).toHaveBeenCalledWith('Please fill in required fields');
    expect(organizationDataServiceSpy.createOrganizationByUser).not.toHaveBeenCalled();
  });

  it('should update organization when form is submitted', () => {
    component.currentOrganization = mockOrganization;
    component.updateOrgForm = {
      name: 'Updated Acme Corporation',
      branding: { logoUrl: 'https://acme.com/new-logo.png', primaryColor: '#ff6600' }
    };

    organizationDataServiceSpy.updateOrganization.and.returnValue(of(mockOrganization));

    component.updateOrganization();

    expect(organizationDataServiceSpy.updateOrganization).toHaveBeenCalledWith(
      mockOrganization._id,
      component.updateOrgForm
    );
  });

  it('should delete organization with confirmation', () => {
    spyOn(window, 'confirm').and.returnValue(true);
    spyOn(window, 'alert');
    component.currentOrganization = mockOrganization;

    const deleteResponse = { success: true, message: 'Organization deleted successfully' };
    organizationDataServiceSpy.deleteOrganization.and.returnValue(of(deleteResponse));

    component.deleteOrganization();

    expect(window.confirm).toHaveBeenCalled();
    expect(organizationDataServiceSpy.deleteOrganization).toHaveBeenCalledWith(mockOrganization._id);
    expect(window.alert).toHaveBeenCalledWith('Organization deleted successfully');
  });

  it('should not delete organization without confirmation', () => {
    spyOn(window, 'confirm').and.returnValue(false);
    component.currentOrganization = mockOrganization;

    component.deleteOrganization();

    expect(window.confirm).toHaveBeenCalled();
    expect(organizationDataServiceSpy.deleteOrganization).not.toHaveBeenCalled();
  });

  it('should select organization when clicked', () => {
    component.selectOrganization(mockOrganization);

    expect(organizationDataServiceSpy.setCurrentOrganization).toHaveBeenCalledWith(mockOrganization);
  });

  it('should load organization details', () => {
    const detailsResponse = {
      success: true,
      data: { ...mockOrganization, members: [] },
      metadata: { lookup_method: 'objectId' as const, member_count: 0, retrieved_at: new Date() }
    };
    organizationDataServiceSpy.loadOrganizationDetails.and.returnValue(of(detailsResponse));

    component.loadOrganizationDetails(mockOrganization._id);

    expect(organizationDataServiceSpy.loadOrganizationDetails).toHaveBeenCalledWith(mockOrganization._id);
  });

  it('should add user to organization', () => {
    component.currentOrganization = mockOrganization;
    component.addUserForm = {
      email: '<EMAIL>',
      role: 'orgmember'
    };

    spyOn(window, 'alert');
    component.addUserToOrganization();

    expect(mockOrganizationApi.addUserToOrganization).toHaveBeenCalledWith(
      mockOrganization._id,
      component.addUserForm
    );
    expect(window.alert).toHaveBeenCalledWith('User added successfully');
  });

  it('should show alert when adding user without email', () => {
    spyOn(window, 'alert');
    component.currentOrganization = mockOrganization;
    component.addUserForm = { email: '', role: 'orgmember' };

    component.addUserToOrganization();

    expect(window.alert).toHaveBeenCalledWith('Please select an organization and enter user email');
    expect(mockOrganizationApi.addUserToOrganization).not.toHaveBeenCalled();
  });

  it('should remove user from organization', () => {
    component.currentOrganization = mockOrganization;
    spyOn(window, 'alert');

    component.removeUserFromOrganization('<EMAIL>');

    expect(mockOrganizationApi.removeUserFromOrganization).toHaveBeenCalledWith(
      mockOrganization._id,
      { email: '<EMAIL>' }
    );
    expect(window.alert).toHaveBeenCalledWith('User removed successfully');
  });

  it('should refresh current organization', () => {
    organizationDataServiceSpy.refreshCurrentOrganization.and.returnValue(of(mockOrganization));

    component.refreshCurrentOrganization();

    expect(organizationDataServiceSpy.refreshCurrentOrganization).toHaveBeenCalled();
  });

  it('should reset create form', () => {
    component.createOrgForm = {
      name: 'Test',
      subdomain: 'test',
      branding: { logoUrl: 'test.png', primaryColor: '#000000' }
    };

    component.resetCreateForm();

    expect(component.createOrgForm.name).toBe('');
    expect(component.createOrgForm.subdomain).toBe('');
    expect(component.createOrgForm.branding?.logoUrl).toBe('');
    expect(component.createOrgForm.branding?.primaryColor).toBe('#336699');
  });

  it('should reset add user form', () => {
    component.addUserForm = { email: '<EMAIL>', role: 'admin' };

    component.resetAddUserForm();

    expect(component.addUserForm.email).toBe('');
    expect(component.addUserForm.role).toBe('orgmember');
  });

  it('should handle loading state', () => {
    Object.defineProperty(organizationDataServiceSpy, 'isLoading$', {
      value: of(true)
    });

    fixture.detectChanges();

    expect(component.isLoading).toBe(true);

    const loadingIndicator = fixture.debugElement.query(By.css('.loading-indicator'));
    expect(loadingIndicator).toBeTruthy();
  });

  it('should handle errors when creating organization', () => {
    spyOn(console, 'error');
    spyOn(window, 'alert');
    organizationDataServiceSpy.createOrganizationByUser.and.returnValue(
      throwError(() => new Error('Creation failed'))
    );

    component.createOrgForm = {
      name: 'Test Org',
      subdomain: 'test-org',
      branding: { logoUrl: '', primaryColor: '#336699' }
    };

    component.createOrganization();

    expect(console.error).toHaveBeenCalledWith('Failed to create organization:', jasmine.any(Error));
    expect(window.alert).toHaveBeenCalledWith('Failed to create organization. Please try again.');
  });

  it('should properly clean up subscriptions on destroy', () => {
    spyOn(component['destroy$'], 'next');
    spyOn(component['destroy$'], 'complete');

    component.ngOnDestroy();

    expect(component['destroy$'].next).toHaveBeenCalled();
    expect(component['destroy$'].complete).toHaveBeenCalled();
  });

  it('should return correct organization name', () => {
    component.currentOrganization = mockOrganization;
    expect(component.organizationName).toBe('Acme Corporation');

    component.currentOrganization = null;
    expect(component.organizationName).toBe('No Organization Selected');
  });

  it('should return correct organization subdomain', () => {
    component.currentOrganization = mockOrganization;
    expect(component.organizationSubdomain).toBe('acme-corp');

    component.currentOrganization = null;
    expect(component.organizationSubdomain).toBe('');
  });

  it('should return correct organization status', () => {
    component.currentOrganization = mockOrganization;
    expect(component.organizationStatus).toBe('active');

    component.currentOrganization = null;
    expect(component.organizationStatus).toBe('unknown');
  });
});
