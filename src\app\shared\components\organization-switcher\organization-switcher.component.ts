import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AuthService } from '../../../core/services/auth/auth.service';
import { SubdomainService } from '../../../core/services/subdomain/subdomain.service';
import { CompleteUser, Organization } from '../../../core/models/user.model';

@Component({
  selector: 'app-organization-switcher',
  templateUrl: './organization-switcher.component.html',
  styles: [
    `
      :host {
        display: block;
      }
    `,
  ],
})
export class OrganizationSwitcherComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  currentUser: CompleteUser | null = null;
  userOrganizations: Organization[] = [];
  currentOrganization: Organization | null = null;
  isDropdownOpen = false;

  constructor(
    private authService: AuthService,
    private subdomainService: SubdomainService
  ) {}

  ngOnInit(): void {
    this.loadUserData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadUserData(): void {
    // Get current user data
    this.currentUser = this.authService.getCompleteUser();

    if (this.currentUser) {
      this.userOrganizations = this.currentUser.organizations || [];
      this.setCurrentOrganization();
    }

    // Subscribe to user data changes
    this.authService.completeUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe((user) => {
        this.currentUser = user;
        if (user) {
          this.userOrganizations = user.organizations || [];
          this.setCurrentOrganization();
        }
      });
  }

  private setCurrentOrganization(): void {
    const currentSubdomain = this.subdomainService.getCurrentSubdomain();

    if (currentSubdomain) {
      // Find organization matching current subdomain
      this.currentOrganization =
        this.userOrganizations.find(
          (org) => org.subdomain === currentSubdomain
        ) || null;
    } else {
      // Use default organization or first organization
      this.currentOrganization =
        this.currentUser?.defaultOrganization ||
        (this.userOrganizations.length > 0 ? this.userOrganizations[0] : null);
    }
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
  }

  switchToOrganization(organization: Organization): void {
    console.log(
      'Switching to organization:',
      organization.name,
      organization.subdomain
    );

    this.closeDropdown();

    // Don't redirect if already on the same organization
    if (this.isCurrentOrganization(organization)) {
      console.log('Already on this organization subdomain');
      return;
    }

    // Redirect to the selected organization's subdomain
    if (organization.subdomain) {
      this.subdomainService.redirectToSubdomain(
        organization.subdomain,
        '/org-dashboard'
      );
    }
  }

  isCurrentOrganization(organization: Organization): boolean {
    const currentSubdomain = this.subdomainService.getCurrentSubdomain();
    return organization.subdomain === currentSubdomain;
  }

  isDefaultOrganization(organization: Organization): boolean {
    return this.currentUser?.defaultOrganization?._id === organization._id;
  }

  getOrganizationInitials(organization: Organization | null): string {
    if (!organization?.name) return '?';

    return organization.name
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
  }

  onImageError(event: any, organization: Organization): void {
    console.log('Organization logo failed to load for:', organization.name);
    // The template will automatically show the initials avatar due to the *ngIf condition
  }

  trackByOrgId(index: number, org: Organization): string {
    return org._id;
  }
}
