/* Import Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Inter font from @fontsource/inter */
@import "@fontsource/inter/400.css";
@import "@fontsource/inter/500.css";
@import "@fontsource/inter/600.css";
@import "@fontsource/inter/700.css";

/* Global styles */
html,
body {
  height: 100%;
  font-family: "Inter", sans-serif;
  color: #1f2937;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.btn-custom:hover {
  background-color: #0056b3;
}

.page-container {
  background-color: white;
}

h1 {
  @apply text-4xl sm:text-5xl;
}

h2 {
  @apply text-3xl sm:text-4xl;
}

h3 {
  @apply text-2xl sm:text-3xl;
}

h4 {
  @apply text-xl sm:text-2xl;
}

h5 {
  @apply text-lg sm:text-xl;
}

h6 {
  @apply text-base sm:text-lg;
}

p {
  @apply text-[18px] sm:text-[20px] md:text-[22px] leading-7;
}

ul {
  @apply list-disc list-inside;
}

ol {
  @apply list-decimal list-inside;
}

/* Custom utility classes */
.transition-default {
  transition: all 200ms ease-in-out;
}

/* Button styles */
.btn-primary {
  padding: 0.5rem 1rem;
  background-color: #4f46e5;
  color: white;
  border-radius: 0.375rem;
  transition: all 200ms ease-in-out;
}

.btn-primary:hover {
  background-color: #4338ca;
}

/* Form control styles */
.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 0.875rem;
}

.form-control:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 1px #4f46e5;
}

/* Card styles */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
}
