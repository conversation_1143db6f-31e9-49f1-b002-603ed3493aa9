<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center items-center h-64">
    <div
      class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"
    ></div>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading && (completeUser$ | async) as user" class="space-y-8">
    <!-- Welcome Header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            Welcome back, {{ user.name }}!
          </h1>
          <p class="mt-1 text-sm text-gray-600">
            {{ getUserTypeDisplay(user.type) }} Account
          </p>
        </div>
        <div class="flex space-x-3">
          <button
            (click)="editProfile()"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Edit Profile
          </button>
          <button
            *ngIf="user.type === 'organization'"
            (click)="editOrganization()"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Manage Organization
          </button>
        </div>
      </div>
    </div>

    <!-- Individual Dashboard -->
    <div
      *ngIf="user.type === 'individual'"
      class="grid grid-cols-1 lg:grid-cols-3 gap-8"
    >
      <!-- Profile Summary -->
      <div class="lg:col-span-2">
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-4">
            Profile Summary
          </h2>
          <div class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  {{ user.profile?.fullName || user.name }}
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Job Title</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  {{ user.profile?.jobTitle || "Not specified" }}
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Company</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  {{ user.profile?.companyName || "Not specified" }}
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Email</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ user.email }}</dd>
              </div>
            </div>
            <div *ngIf="user.profile?.bio">
              <dt class="text-sm font-medium text-gray-500">Bio</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ user.profile.bio }}</dd>
            </div>
            <div *ngIf="user.profile?.networkingGoal">
              <dt class="text-sm font-medium text-gray-500">Networking Goal</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ user.profile.networkingGoal }}
              </dd>
            </div>
            <div
              *ngIf="
                user.profile?.industryTags &&
                (user.profile?.industryTags?.length || 0) > 0
              "
            >
              <dt class="text-sm font-medium text-gray-500">Industry Tags</dt>
              <dd class="mt-1">
                <div class="flex flex-wrap gap-2">
                  <span
                    *ngFor="let tag of user.profile.industryTags"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                  >
                    {{ tag }}
                  </span>
                </div>
              </dd>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="space-y-6">
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div class="space-y-3">
            <button
              class="w-full text-left px-4 py-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div class="font-medium text-gray-900">Browse Events</div>
              <div class="text-sm text-gray-500">
                Find networking events to attend
              </div>
            </button>
            <button
              class="w-full text-left px-4 py-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div class="font-medium text-gray-900">My Connections</div>
              <div class="text-sm text-gray-500">
                Manage your professional network
              </div>
            </button>
            <button
              class="w-full text-left px-4 py-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div class="font-medium text-gray-900">Calendar</div>
              <div class="text-sm text-gray-500">
                View upcoming events and meetings
              </div>
            </button>
          </div>
        </div>

        <!-- Delegate Information -->
        <div
          *ngIf="user.profile?.delegateEmail"
          class="bg-white shadow rounded-lg p-6"
        >
          <h3 class="text-lg font-medium text-gray-900 mb-4">Delegate</h3>
          <div>
            <dt class="text-sm font-medium text-gray-500">Delegate Email</dt>
            <dd class="mt-1 text-sm text-gray-900">
              {{ user.profile.delegateEmail }}
            </dd>
          </div>
        </div>
      </div>
    </div>

    <!-- Organization Dashboard -->
    <div
      *ngIf="user.type === 'organization'"
      class="grid grid-cols-1 lg:grid-cols-3 gap-8"
    >
      <!-- Organization Details -->
      <div class="lg:col-span-2">
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-4">
            Organization Details
          </h2>
          <div class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">
                  Organization Name
                </dt>
                <dd class="mt-1 text-sm text-gray-900">{{ user.name }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Contact Email</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ user.email }}</dd>
              </div>
              <div *ngIf="user.organizations && user.organizations.length > 0">
                <dt class="text-sm font-medium text-gray-500">Subdomain</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <a
                    [href]="
                      'https://' +
                      user.organizations[0].subdomain +
                      '.digimeet.io'
                    "
                    target="_blank"
                    class="text-indigo-600 hover:text-indigo-500"
                  >
                    {{ user.organizations[0].subdomain }}.digimeet.io
                  </a>
                </dd>
              </div>
              <div *ngIf="user.phone_number">
                <dt class="text-sm font-medium text-gray-500">Phone</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  {{ user.phone_number }}
                </dd>
              </div>
            </div>

            <!-- Organization Roles -->
            <div *ngIf="getOrganizationRoles(user).length > 0">
              <dt class="text-sm font-medium text-gray-500">
                Organization Roles
              </dt>
              <dd class="mt-1">
                <div class="flex flex-wrap gap-2">
                  <span
                    *ngFor="let role of getOrganizationRoles(user)"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                  >
                    {{ role | titlecase }}
                  </span>
                </div>
              </dd>
            </div>
          </div>
        </div>
      </div>

      <!-- Organization Actions -->
      <div class="space-y-6">
        <!-- Role-based Features -->
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Organization Features
          </h3>
          <div class="space-y-3">
            <!-- Sponsor Features -->
            <div
              *ngIf="hasRole(user, 'sponsor')"
              class="border-l-4 border-blue-400 pl-4"
            >
              <h4 class="font-medium text-gray-900 mb-2">Sponsor Features</h4>
              <div class="space-y-2">
                <button
                  class="w-full text-left px-3 py-2 text-sm border border-gray-200 rounded hover:bg-gray-50 transition-colors"
                >
                  Exhibit at Events
                </button>
                <button
                  class="w-full text-left px-3 py-2 text-sm border border-gray-200 rounded hover:bg-gray-50 transition-colors"
                >
                  Capture Leads
                </button>
                <button
                  class="w-full text-left px-3 py-2 text-sm border border-gray-200 rounded hover:bg-gray-50 transition-colors"
                >
                  Brand Representation
                </button>
              </div>
            </div>

            <!-- Organizer Features -->
            <div
              *ngIf="hasRole(user, 'organizer')"
              class="border-l-4 border-green-400 pl-4"
            >
              <h4 class="font-medium text-gray-900 mb-2">Organizer Features</h4>
              <div class="space-y-2">
                <button
                  class="w-full text-left px-3 py-2 text-sm border border-gray-200 rounded hover:bg-gray-50 transition-colors"
                >
                  Create Events
                </button>
                <button
                  class="w-full text-left px-3 py-2 text-sm border border-gray-200 rounded hover:bg-gray-50 transition-colors"
                >
                  Manage Events
                </button>
                <button
                  class="w-full text-left px-3 py-2 text-sm border border-gray-200 rounded hover:bg-gray-50 transition-colors"
                >
                  Sell Tickets
                </button>
                <button
                  class="w-full text-left px-3 py-2 text-sm border border-gray-200 rounded hover:bg-gray-50 transition-colors"
                >
                  Audience Analytics
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Team Management -->
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Team Management
          </h3>
          <div class="space-y-3">
            <button
              class="w-full text-left px-4 py-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div class="font-medium text-gray-900">Manage Members</div>
              <div class="text-sm text-gray-500">
                Add, remove, and manage team roles
              </div>
            </button>
            <button
              class="w-full text-left px-4 py-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div class="font-medium text-gray-900">Organization Settings</div>
              <div class="text-sm text-gray-500">
                Update organization profile and preferences
              </div>
            </button>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Stats</h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">Team Members</span>
              <span class="text-sm font-medium text-gray-900">{{
                user.roles?.length || 0
              }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">Organizations</span>
              <span class="text-sm font-medium text-gray-900">{{
                user.organizations?.length || 0
              }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">Account Status</span>
              <span class="text-sm font-medium text-green-600">{{
                user.status | titlecase
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
