import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';

import { OrganizationLayoutComponent } from './organization-layout.component';
import { AuthService } from '../../core/services/auth/auth.service';
import { OrganizationDataService } from '../../core/services/organization-data/organization-data.service';
import { UserDataService } from '../../core/services/user-data/user-data.service';
import { CompleteUser, Organization } from '../../core/models/user.model';

describe('OrganizationLayoutComponent', () => {
  let component: OrganizationLayoutComponent;
  let fixture: ComponentFixture<OrganizationLayoutComponent>;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let organizationDataServiceSpy: jasmine.SpyObj<OrganizationDataService>;
  let userDataServiceSpy: jasmine.SpyObj<UserDataService>;

  const mockOrganization: Organization = {
    _id: 'org123',
    name: 'Test Organization',
    subdomain: 'test-org',
    branding: {
      logoUrl: 'https://example.com/logo.png',
      primaryColor: '#336699'
    },
    industry: 'Technology',
    contactEmail: '<EMAIL>',
    status: 'active',
    roles: [],
    members: [],
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockCompleteUser: CompleteUser = {
    _id: '123',
    name: 'Test User',
    email: '<EMAIL>',
    phone_number: '+1234567890',
    type: 'organization',
    status: 'active',
    profile: {},
    roles: [],
    systemPrivileges: [],
    organizations: [mockOrganization],
    privilegeSummary: {
      isGodSuperUser: false,
      hasSystemPrivileges: false,
      highestPrivilegeLevel: 0,
      organizationCount: 1,
    },
  };

  beforeEach(async () => {
    const authSpy = jasmine.createSpyObj('AuthService', ['logout']);
    const orgDataSpy = jasmine.createSpyObj('OrganizationDataService', ['setCurrentOrganization']);
    const userDataSpy = jasmine.createSpyObj('UserDataService', ['loadCompleteUserData'], {
      completeUser$: of(mockCompleteUser),
      isLoading$: of(false)
    });

    await TestBed.configureTestingModule({
      declarations: [OrganizationLayoutComponent],
      imports: [RouterTestingModule],
      providers: [
        { provide: AuthService, useValue: authSpy },
        { provide: OrganizationDataService, useValue: orgDataSpy },
        { provide: UserDataService, useValue: userDataSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(OrganizationLayoutComponent);
    component = fixture.componentInstance;
    authServiceSpy = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    organizationDataServiceSpy = TestBed.inject(OrganizationDataService) as jasmine.SpyObj<OrganizationDataService>;
    userDataServiceSpy = TestBed.inject(UserDataService) as jasmine.SpyObj<UserDataService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load user and organization data on init', () => {
    userDataServiceSpy.loadCompleteUserData.and.returnValue(of(mockCompleteUser));
    
    component.ngOnInit();
    
    expect(component.currentUser).toEqual(mockCompleteUser);
    expect(component.currentOrganization).toEqual(mockOrganization);
    expect(organizationDataServiceSpy.setCurrentOrganization).toHaveBeenCalledWith(mockOrganization);
  });

  it('should toggle sidebar', () => {
    expect(component.isSidebarOpen).toBeFalse();
    
    component.toggleSidebar();
    
    expect(component.isSidebarOpen).toBeTrue();
  });

  it('should call logout on auth service', () => {
    component.logout();
    
    expect(authServiceSpy.logout).toHaveBeenCalled();
  });

  it('should return organization name', () => {
    component.currentOrganization = mockOrganization;
    
    expect(component.organizationName).toBe('Test Organization');
  });

  it('should return default organization name when no organization', () => {
    component.currentOrganization = null;
    
    expect(component.organizationName).toBe('Organization');
  });

  it('should return user name', () => {
    component.currentUser = mockCompleteUser;
    
    expect(component.userName).toBe('Test User');
  });

  it('should return user email', () => {
    component.currentUser = mockCompleteUser;
    
    expect(component.userEmail).toBe('<EMAIL>');
  });
});
