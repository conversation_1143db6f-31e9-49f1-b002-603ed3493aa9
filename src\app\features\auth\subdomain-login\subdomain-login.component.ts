import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthService } from '../../../core/services/auth/auth.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-subdomain-login',
  template: `
    <div class="min-h-screen flex items-center justify-center bg-gray-50">
      <div class="max-w-md w-full space-y-8">
        <div class="text-center">
          <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
            Setting up your organization dashboard...
          </h2>
          <p class="mt-2 text-sm text-gray-600">
            Please wait while we authenticate you on this subdomain.
          </p>
        </div>

        <div class="flex justify-center">
          <div
            class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"
          ></div>
        </div>

        <div
          *ngIf="error"
          class="bg-red-50 border border-red-200 rounded-md p-4"
        >
          <div class="flex">
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                Authentication Error
              </h3>
              <div class="mt-2 text-sm text-red-700">
                {{ error }}
              </div>
              <div class="mt-4">
                <button
                  type="button"
                  class="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                  (click)="redirectToMainDomain()"
                >
                  Return to Main Site
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Debug Information -->
        <div class="mt-4 p-4 bg-gray-100 rounded-md text-xs">
          <h4 class="font-semibold mb-2">Debug Information:</h4>
          <div class="space-y-1">
            <div>Current URL: {{ getCurrentUrl() }}</div>
            <div>Has Token: {{ hasToken() }}</div>
            <div>Has Refresh Token: {{ hasRefreshToken() }}</div>
            <div>
              AuthService Authenticated: {{ isAuthServiceAuthenticated() }}
            </div>
            <div>Redirect Path: {{ getRedirectPath() }}</div>
          </div>
          <button
            type="button"
            class="mt-2 bg-blue-100 px-2 py-1 rounded text-blue-800 hover:bg-blue-200"
            (click)="showDebugInfo()"
          >
            Show Console Debug
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [],
})
export class SubdomainLoginComponent implements OnInit {
  error: string | null = null;
  private currentRedirectPath: string = 'org-dashboard';

  constructor(
    private route: ActivatedRoute,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    console.log(
      'SubdomainLoginComponent: Processing subdomain authentication...'
    );

    // Get tokens from URL parameters
    this.route.queryParams.subscribe((params) => {
      const token = params['token'];
      const refreshToken = params['refresh'];
      const redirectPath = params['redirect'] || 'org-dashboard';
      this.currentRedirectPath = redirectPath;

      console.log('SubdomainLoginComponent: Received parameters:', {
        hasToken: !!token,
        hasRefreshToken: !!refreshToken,
        redirectPath,
      });

      if (token && refreshToken) {
        this.authenticateWithTokens(token, refreshToken, redirectPath);
      } else {
        this.error = 'Missing authentication tokens. Please login again.';
        console.error(
          'SubdomainLoginComponent: Missing tokens in URL parameters'
        );
      }
    });
  }

  private authenticateWithTokens(
    token: string,
    refreshToken: string,
    redirectPath: string
  ): void {
    try {
      console.log('SubdomainLoginComponent: Setting tokens in localStorage...');
      console.log('SubdomainLoginComponent: Token length:', token.length);
      console.log(
        'SubdomainLoginComponent: Refresh token length:',
        refreshToken.length
      );

      // Store tokens using the correct keys that AuthService expects
      localStorage.setItem('access_token', token);
      localStorage.setItem('refresh_token', refreshToken);
      localStorage.setItem('token_type', 'Bearer');

      // Also store in cookie format for cross-subdomain compatibility
      localStorage.setItem('idToken', token);
      localStorage.setItem('refreshToken', refreshToken);

      // Verify tokens were stored
      const storedAccessToken = localStorage.getItem('access_token');
      const storedRefreshToken = localStorage.getItem('refresh_token');
      console.log('SubdomainLoginComponent: Tokens stored successfully:', {
        hasStoredAccessToken: !!storedAccessToken,
        hasStoredRefreshToken: !!storedRefreshToken,
        tokenType: localStorage.getItem('token_type'),
      });

      // Add a timeout to prevent infinite loading
      const timeoutId = setTimeout(() => {
        console.error(
          'SubdomainLoginComponent: Authentication timeout after 15 seconds'
        );
        this.error = 'Authentication timeout. Please try again.';
      }, 15000);

      // Now fetch user data and set up authentication state properly
      console.log('SubdomainLoginComponent: Fetching user data...');

      // Use a small delay to ensure tokens are set before making API calls
      setTimeout(() => {
        this.setupAuthenticationState(redirectPath, timeoutId);
      }, 100);
    } catch (error) {
      console.error('SubdomainLoginComponent: Error setting tokens:', error);
      this.error = 'Authentication failed. Please login again.';
    }
  }

  private setupAuthenticationState(redirectPath: string, timeoutId: any): void {
    // Check if AuthService recognizes the tokens
    const isAuthenticated = this.authService.isAuthenticated();
    console.log(
      'SubdomainLoginComponent: AuthService authentication check:',
      isAuthenticated
    );

    if (!isAuthenticated) {
      console.error(
        'SubdomainLoginComponent: AuthService does not recognize tokens'
      );
      this.error = 'Token validation failed. Please login again.';
      clearTimeout(timeoutId);
      return;
    }

    // For organization users on subdomain, skip user data fetching to avoid CORS issues
    // The organization layout component will handle loading user data once properly redirected
    console.log(
      'SubdomainLoginComponent: Skipping user data fetch for organization user on subdomain'
    );
    console.log(
      'SubdomainLoginComponent: Proceeding directly to redirect:',
      redirectPath
    );

    clearTimeout(timeoutId);

    // Add a small delay to ensure tokens are fully processed, then redirect to loading page
    setTimeout(() => {
      console.log(
        'SubdomainLoginComponent: Redirecting to loading page to prevent CORS issues'
      );

      // Redirect to the subdomain loading component instead of directly to org-dashboard
      // This will handle the delayed API calls and provide a better user experience
      const currentOrigin = window.location.origin;
      const loadingUrl = `${currentOrigin}/subdomain-loading`;

      console.log('SubdomainLoginComponent: Redirecting to:', loadingUrl);

      // Use window.location.href for a full page reload to ensure clean state
      window.location.href = loadingUrl;
    }, 200);
  }

  redirectToMainDomain(): void {
    // Redirect back to main domain login
    const protocol = window.location.protocol;
    const mainDomain = environment.production
      ? `${protocol}//${environment.appDomain}`
      : `${protocol}//localhost:4200`;
    window.location.href = mainDomain + '/auth/login';
  }

  // Debug methods for template
  getCurrentUrl(): string {
    return window.location.href;
  }

  hasToken(): boolean {
    return (
      !!localStorage.getItem('access_token') ||
      !!localStorage.getItem('idToken')
    );
  }

  hasRefreshToken(): boolean {
    return (
      !!localStorage.getItem('refresh_token') ||
      !!localStorage.getItem('refreshToken')
    );
  }

  isAuthServiceAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }

  getRedirectPath(): string {
    return this.currentRedirectPath;
  }

  showDebugInfo(): void {
    console.log('=== SUBDOMAIN LOGIN DEBUG INFO ===');
    console.log('Current URL:', this.getCurrentUrl());
    console.log('Has access_token:', !!localStorage.getItem('access_token'));
    console.log('Has refresh_token:', !!localStorage.getItem('refresh_token'));
    console.log('Has idToken:', !!localStorage.getItem('idToken'));
    console.log('Has refreshToken:', !!localStorage.getItem('refreshToken'));
    console.log('Token type:', localStorage.getItem('token_type'));
    console.log(
      'AuthService authenticated:',
      this.authService.isAuthenticated()
    );
    console.log('Redirect path:', this.currentRedirectPath);
    console.log('Error state:', this.error);
    console.log('All localStorage keys:', Object.keys(localStorage));
  }
}
