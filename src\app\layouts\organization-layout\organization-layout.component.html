<div class="min-h-screen bg-gray-50">
  <!-- Organization Header -->
  <header class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Left side: Organization branding and navigation -->
        <div class="flex items-center">
          <!-- Mobile menu button -->
          <button
            type="button"
            class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
            (click)="toggleSidebar()"
          >
            <svg
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>

          <!-- Organization Logo and Name -->
          <div class="flex items-center ml-4 md:ml-0">
            <div class="flex-shrink-0">
              <img
                class="h-8 w-8 rounded-full"
                [src]="getOrganizationLogoUrl()"
                [alt]="organizationName + ' logo'"
                (error)="onImageError($event)"
              />
            </div>
            <div class="ml-3">
              <h1 class="text-lg font-semibold text-gray-900">
                {{ organizationName }}
              </h1>
              <p
                class="text-xs text-gray-500"
                *ngIf="currentOrganization?.subdomain"
              >
                {{ getSubdomainUrl() }}
              </p>
            </div>
          </div>

          <!-- Desktop Navigation -->
          <nav class="hidden md:flex md:ml-8 md:space-x-8">
            <a
              routerLink="/dashboard"
              routerLinkActive="border-indigo-500 text-gray-900"
              class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
            >
              Dashboard
            </a>
            <a
              routerLink="/members"
              routerLinkActive="border-indigo-500 text-gray-900"
              class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
            >
              Members
            </a>
            <a
              routerLink="/events"
              routerLinkActive="border-indigo-500 text-gray-900"
              class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
            >
              Events
            </a>
            <a
              routerLink="/settings"
              routerLinkActive="border-indigo-500 text-gray-900"
              class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
            >
              Settings
            </a>
          </nav>
        </div>

        <!-- Right side: User menu -->
        <div class="flex items-center space-x-4">
          <!-- Organization Switcher -->
          <app-organization-switcher></app-organization-switcher>

          <!-- Notifications -->
          <button
            class="p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z"
              />
            </svg>
          </button>

          <!-- User dropdown -->
          <div class="ml-3 relative">
            <div class="flex items-center">
              <button
                class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <img
                  class="h-8 w-8 rounded-full"
                  src="/assets/logo.png"
                  [alt]="userName"
                />
              </button>
              <div class="ml-3 hidden md:block">
                <div class="text-sm font-medium text-gray-700">
                  {{ userName }}
                </div>
                <div class="text-xs text-gray-500">{{ userEmail }}</div>
              </div>
            </div>
          </div>

          <!-- Logout button with text -->
          <button
            (click)="logout()"
            class="ml-4 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
            title="Logout"
          >
            <svg
              class="h-4 w-4 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
              />
            </svg>
            <span class="hidden sm:inline">Logout</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div class="md:hidden" [class.hidden]="!isSidebarOpen">
      <div class="pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
        <a
          routerLink="/dashboard"
          routerLinkActive="bg-indigo-50 border-indigo-500 text-indigo-700"
          class="bg-transparent border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
        >
          Dashboard
        </a>
        <a
          routerLink="/members"
          routerLinkActive="bg-indigo-50 border-indigo-500 text-indigo-700"
          class="bg-transparent border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
        >
          Members
        </a>
        <a
          routerLink="/events"
          routerLinkActive="bg-indigo-50 border-indigo-500 text-indigo-700"
          class="bg-transparent border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
        >
          Events
        </a>
        <a
          routerLink="/settings"
          routerLinkActive="bg-indigo-50 border-indigo-500 text-indigo-700"
          class="bg-transparent border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
        >
          Settings
        </a>

        <!-- Mobile Logout Button -->
        <button
          (click)="logout()"
          class="w-full text-left bg-transparent border-transparent text-red-600 hover:bg-red-50 hover:border-red-300 hover:text-red-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
        >
          <svg
            class="h-5 w-5 inline mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
            />
          </svg>
          Logout
        </button>
      </div>
    </div>
  </header>

  <!-- Main Content Area -->
  <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Subdomain Security Test (Debug Only) -->
    <app-subdomain-test></app-subdomain-test>

    <!-- Loading indicator -->
    <div *ngIf="isLoading" class="flex justify-center items-center py-8">
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"
      ></div>
    </div>

    <!-- Page content -->
    <div [class.opacity-50]="isLoading">
      <router-outlet></router-outlet>
    </div>
  </main>

  <!-- Organization Footer -->
  <footer class="bg-white border-t border-gray-200 mt-auto">
    <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-500">
          © 2024 {{ organizationName }}. Powered by DigiMeet.
        </div>
        <div class="flex space-x-4">
          <a href="#" class="text-sm text-gray-500 hover:text-gray-700"
            >Privacy</a
          >
          <a href="#" class="text-sm text-gray-500 hover:text-gray-700"
            >Terms</a
          >
          <a href="#" class="text-sm text-gray-500 hover:text-gray-700"
            >Support</a
          >
        </div>
      </div>
    </div>
  </footer>
</div>
