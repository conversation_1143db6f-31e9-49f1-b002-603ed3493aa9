import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth/auth.service';
import { UserDataService } from '../services/user-data/user-data.service';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class OrganizationRedirectGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private userDataService: UserDataService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean> {
    console.log('OrganizationRedirectGuard: Checking if user should be redirected to subdomain...');
    
    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      console.log('OrganizationRedirectGuard: User not authenticated, allowing access');
      return of(true);
    }

    // Load fresh user data to check organization status
    return this.userDataService.refreshUserData().pipe(
      map((completeUser) => {
        if (!completeUser) {
          console.log('OrganizationRedirectGuard: No user data, allowing access');
          return true;
        }

        console.log('OrganizationRedirectGuard: User data loaded:', {
          type: completeUser.type,
          organizationCount: completeUser.organizations?.length || 0
        });

        // If user is organization type, they should be on subdomain
        if (completeUser.type === 'organization') {
          const userOrgs = completeUser.organizations || [];
          
          if (userOrgs.length > 0) {
            const primaryOrg = userOrgs[0];
            
            if (primaryOrg.subdomain) {
              const subdomainHost = environment.subdomainPattern.replace(
                '{subdomain}',
                primaryOrg.subdomain
              );
              const currentHost = window.location.host;
              
              console.log('OrganizationRedirectGuard: Organization user detected:', {
                currentHost,
                expectedSubdomainHost: subdomainHost,
                subdomain: primaryOrg.subdomain
              });

              if (currentHost !== subdomainHost) {
                // Organization user trying to access main domain - redirect to subdomain
                console.log('OrganizationRedirectGuard: Redirecting organization user to subdomain');
                const protocol = window.location.protocol;
                const subdomainUrl = `${protocol}//${subdomainHost}/org-dashboard`;
                window.location.href = subdomainUrl;
                return false; // Block access to main domain
              }
            }
          }
        }

        // Allow access for individual users or if already on correct subdomain
        console.log('OrganizationRedirectGuard: Allowing access');
        return true;
      }),
      catchError((error) => {
        console.error('OrganizationRedirectGuard: Error checking user data:', error);
        // Allow access on error to prevent blocking
        return of(true);
      })
    );
  }
}
