/* Organization Layout Specific Styles */

.organization-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.organization-logo {
  transition: transform 0.2s ease-in-out;
}

.organization-logo:hover {
  transform: scale(1.05);
}

.nav-link {
  position: relative;
  transition: all 0.2s ease-in-out;
}

.nav-link:hover {
  transform: translateY(-1px);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 1px;
}

.user-avatar {
  transition: box-shadow 0.2s ease-in-out;
}

.user-avatar:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mobile-menu {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95);
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Custom scrollbar for mobile menu */
.mobile-menu::-webkit-scrollbar {
  width: 4px;
}

.mobile-menu::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.mobile-menu::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.mobile-menu::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Organization branding customization */
.org-primary-color {
  color: var(--org-primary-color, #667eea);
}

.org-primary-bg {
  background-color: var(--org-primary-color, #667eea);
}

.org-primary-border {
  border-color: var(--org-primary-color, #667eea);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .organization-header {
    padding: 0.5rem 1rem;
  }
  
  .nav-link {
    padding: 0.75rem 1rem;
  }
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Footer styling */
footer {
  margin-top: auto;
}

.min-h-screen {
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}
