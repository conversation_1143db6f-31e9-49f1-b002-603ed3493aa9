import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { UserDataService } from '../../../../core/services/user-data/user-data.service';
import { OrganizationDataService } from '../../../../core/services/organization-data/organization-data.service';
import { CompleteUser } from '../../../../core/models/user.model';

@Component({
  selector: 'app-overview',
  templateUrl: './overview.component.html',
  styleUrls: ['./overview.component.css'],
})
export class OverviewComponent implements OnInit {
  completeUser$: Observable<CompleteUser | null>;
  isLoading = false;

  constructor(
    private userDataService: UserDataService,
    private organizationDataService: OrganizationDataService,
    private router: Router
  ) {
    this.completeUser$ = this.userDataService.completeUser$;
  }

  ngOnInit(): void {
    this.loadUserData();
  }

  private loadUserData(): void {
    this.isLoading = true;
    this.userDataService.refreshUserData().subscribe({
      next: () => {
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading user data:', error);
        this.isLoading = false;
      },
    });
  }

  editProfile(): void {
    this.router.navigate(['/dashboard/profile']);
  }

  editOrganization(): void {
    this.router.navigate(['/dashboard/organization']);
  }

  getUserTypeDisplay(type: string): string {
    return type === 'individual' ? 'Individual' : 'Organization';
  }

  getOrganizationRoles(user: CompleteUser): string[] {
    return user.roles?.map((role) => role.role) || [];
  }

  hasRole(user: CompleteUser, role: string): boolean {
    return this.getOrganizationRoles(user).includes(role);
  }
}
